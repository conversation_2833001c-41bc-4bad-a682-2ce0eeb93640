using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class LAPBKItemRepository : ILAPBKItemRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public LAPBKItemRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<LAPBKItem> SaveAsync(LAPBKItem lapbkItem)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (lapbkItem.Id == 0)
                {
                    // Create new record
                    _dbContext.LAPBKItems.Add(lapbkItem);
                }
                else
                {
                    // Update existing record
                    _dbContext.LAPBKItems.Update(lapbkItem);
                }

                await _dbContext.SaveChangesAsync();
                return lapbkItem;
            }
        }

        public async Task<List<LAPBKItem>> GetItemsBySubscaleAsync(long? organizationId, long? assessmentId, long? subscaleId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            if (subscaleId is null)
            {
                throw new ArgumentNullException(nameof(subscaleId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<LAPBKItem> items = await _dbContext.LAPBKItems
                    .Where(i => i.OrganizationId == organizationId.Value
                             && i.AssessmentId == assessmentId.Value
                             && i.SubscaleId == subscaleId.Value)
                    .ToListAsync();

                return items;
            }
        }

        public async Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId, long? subscaleId)
        {
            string sqlQuery = @"SELECT itm.InstID AS Id, itm.AchieveDate, itm.AssessDate, itm.AssessmentInstID, sub.ChronologicalAge, itm.CustomerInstID,
		                                lkup_sub.Domain, lkup_sub.DomainSequence, lkup_sub.SubscaleID + lkup_itm.Sequence AS ItemID,
		                                a.SchoolYear, itm.SelectedItems, itm.Sequence, lkup_sub.Subscale, lkup_sub.SubscaleID, lkup_sub.SubscaleSequence,
		                                itm.UserComment, itm.Value, itm.ChildInstID, itm.ObserverInstID, itm.ItemStaticID, itm.SubscaleStaticID
                                FROM lap_lapbk_items AS itm
                                INNER JOIN lap_lapbk_subscales AS sub
	                                ON sub.InstID = itm.SubscaleInstID
		                                AND sub.organization_id = itm.CustomerInstID
                                INNER JOIN lap_assessments AS a
	                                ON a.InstID = itm.AssessmentInstID
		                                AND a.CustomerInstID = itm.CustomerInstID
                                INNER JOIN lap_subscales_lookup AS lkup_sub
	                                ON lkup_sub.StaticID = itm.SubscaleStaticID
                                INNER JOIN lap_items_lookup AS lkup_itm
	                                ON lkup_itm.StaticId = itm.ItemStaticID
                                INNER JOIN lap_lapbk_observation_items AS obs
	                                ON obs.SourceItemInstID = itm.InstID
                                WHERE itm.CustomerInstID = {0}
	                                AND itm.AssessmentInstID = {1}
	                                AND itm.SubscaleInstID = {2}";

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<AssessmentItem> resultList = await _dbContext.AssessmentItems.FromSqlRaw(sqlQuery, organizationId, assessmentId, subscaleId)
                    .Select(i => new AssessmentItem
                    {
                        Id = i.Id,
                        AchieveDate = i.AchieveDate,
                        AssessDate = i.AssessDate,
                        AssessmentInstID = i.AssessmentInstID,
                        ChronologicalAge = i.ChronologicalAge,
                        CustomerInstID = i.CustomerInstID,
                        Domain = i.Domain,
                        DomainSequence = i.DomainSequence,
                        Instrument = i.Instrument,
                        ItemID = i.ItemID,
                        SchoolYear = i.SchoolYear,
                        SelectedItems = i.SelectedItems,
                        Sequence = i.Sequence,
                        Subscale = i.Subscale,
                        SubscaleID = i.SubscaleID,
                        SubscaleSequence = i.SubscaleSequence,
                        UserComment = i.UserComment,
                        Value = i.Value,
                        ChildInstID = i.ChildInstID,
                        ObserverInstID = i.ObserverInstID,
                        ItemStaticID = i.ItemStaticID,
                        SubscaleStaticID = i.SubscaleStaticID
                    })
                    .ToListAsync();

                return resultList;
            }
        }
    }
}
