namespace Compass.DECA.Models
{
    /// <summary>
    /// Data transfer object for DECA session context
    /// </summary>
    public class DECASessionContext
    {
        /// <summary>
        /// The current rating ID (null indicates creating a new rating)
        /// </summary>
        public long? RatingId { get; set; }

        /// <summary>
        /// The current checkpoint
        /// </summary>
        public int Checkpoint { get; set; }
    }

    /// <summary>
    /// Combined DECA session data
    /// </summary>
    public class DECASessionData
    {
        /// <summary>
        /// Session context data
        /// </summary>
        public DECASessionContext SessionContext { get; set; } = new();
    }
}
