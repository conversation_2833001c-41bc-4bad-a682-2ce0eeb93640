using Compass.LAP.Resources.Instruments;

namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public enum Checkpoint
    {
        NONE = 0,
        PRE_TEST = 1,
        BEGINNING = 1,
        MID_YEAR = 2,
        POST_TEST = 3,
        END_YEAR = 3,
        ONGOING = 4
    }

    public static class CheckpointExtensions
    {
        private static readonly Dictionary<Checkpoint, string> _descriptions = new()
        {
            { Checkpoint.NONE, "NONE" },
            { Checkpoint.PRE_TEST, "Pre" },
            { Checkpoint.BEGINNING, "Beginning" },
            { Checkpoint.MID_YEAR, "Mid" },
            { Checkpoint.POST_TEST, "Post" },
            { Checkpoint.END_YEAR, "End" },
            { Checkpoint.ONGOING, "Ongoing" }
        };

        public static int GetValue(this Checkpoint checkpoint)
        {
            return (int)checkpoint;
        }

        public static string GetDescription(this Checkpoint checkpoint)
        {
            return _descriptions.TryGetValue(checkpoint, out var description) ? description : checkpoint.ToString();
        }

        public static ICollection<Checkpoint> GetCheckpointsForAssessmentLevel(int assessmentLevel)
        {
            var result = new List<Checkpoint>(4);

            if (assessmentLevel == AssessmentLevel.E_LAP || assessmentLevel == AssessmentLevel.LAP_3
                || assessmentLevel == AssessmentLevel.LAP_BK)
            {
                result.Add(Checkpoint.BEGINNING);
                result.Add(Checkpoint.MID_YEAR);
                result.Add(Checkpoint.END_YEAR);
                result.Add(Checkpoint.ONGOING);
            }
            else if (assessmentLevel == AssessmentLevel.LAP_D
                || assessmentLevel == AssessmentLevel.LAP_D3)
            {
                result.Add(Checkpoint.PRE_TEST);
                result.Add(Checkpoint.MID_YEAR);
                result.Add(Checkpoint.POST_TEST);
            }

            return result;
        }

        public static Checkpoint? GetCheckpoint(int? value, int? assessmentLevel)
        {
            if (assessmentLevel == AssessmentLevel.E_LAP || assessmentLevel == AssessmentLevel.LAP_3
                || assessmentLevel == AssessmentLevel.LAP_BK)
            {
                return value switch
                {
                    0 => Checkpoint.NONE,
                    1 => Checkpoint.BEGINNING,
                    2 => Checkpoint.MID_YEAR,
                    3 => Checkpoint.END_YEAR,
                    4 => Checkpoint.ONGOING,
                    _ => null
                };
            }
            else if (assessmentLevel == AssessmentLevel.LAP_D
                || assessmentLevel == AssessmentLevel.LAP_D3)
            {
                return value switch
                {
                    0 => Checkpoint.NONE,
                    1 => Checkpoint.PRE_TEST,
                    2 => Checkpoint.MID_YEAR,
                    3 => Checkpoint.POST_TEST,
                    _ => null
                };
            }

            return null;
        }

        public static string? GetCheckpointDescription(int value, int assessmentLevel)
        {
            var checkpoint = GetCheckpoint(value, assessmentLevel);
            return checkpoint?.GetDescription();
        }

        public static Checkpoint? GetCheckpoint(string description)
        {
            foreach (var checkpoint in Enum.GetValues<Checkpoint>())
            {
                if (description.Contains(checkpoint.GetDescription(), StringComparison.OrdinalIgnoreCase))
                {
                    return checkpoint;
                }
            }
            return null;
        }

        public static int? GetCheckpointValue(string description)
        {
            var checkpoint = GetCheckpoint(description);
            return checkpoint?.GetValue();
        }
    }
}
