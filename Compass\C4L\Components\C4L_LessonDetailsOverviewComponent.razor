@using Compass.C4L.DTOs
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="lesson-section">
    <h2>At a Glance</h2>
    <div>
        @((MarkupString)LessonContentDto.AtAGlance)
    </div>
</div>

<div class="section-divider"></div>

<div class="lesson-section">
    <h2>Learning Objectives</h2>
    @if (LessonContentDto.Objectives.NonAssessed.Count > 0)
    {
        <h3>Non-Assessed Objectives</h3>
        <ul>
            @foreach (string objective in LessonContentDto.Objectives.NonAssessed)
            {
                <li>@((MarkupString)objective)</li>
            }
        </ul>
    }
    else
    {
        <p>No non-assessed objectives for this lesson.</p>
    }
</div>

<div class="section-divider"></div>

<div class="lesson-section">
    <h2>Assessed Objectives</h2>
    @if (LessonContentDto.Objectives.Assessed.Count > 0)
    {
        <ul>
            @foreach (string objective in LessonContentDto.Objectives.Assessed)
            {
                <li>@((MarkupString)objective)</li>
            }
        </ul>
    }
    else
    {
        <p>No assessed objectives for this lesson.</p>
    }
</div>

<div class="section-divider"></div>

<div class="lesson-section">
    <h2>Preparation</h2>
    @foreach (string preparation in LessonContentDto.PreparationTasks)
    {
        @((MarkupString)preparation)
    }

    @if (IsPreparationCompleted)
    {
        <div class="preparation-completed">
            <span class="completed-icon">✓</span> Preparation completed
        </div>
        <button class="c4l-button c4l-primary-button mark-complete-button" @onclick="async () => await UpdateCompletedStatus(false)">
            MARK AS INCOMPLETE
        </button>
    }
    else
    {
        <div class="preparation-completed">
            <span class="completed-icon">✓</span> Preparation not completed
        </div>
        <button class="c4l-button c4l-primary-button mark-complete-button" @onclick="async () => await UpdateCompletedStatus(true)">
            MARK AS COMPLETE
        </button>
    }
</div>

<div class="section-divider"></div>

<div class="lesson-section">
    <h2>Materials</h2>
    @((MarkupString)LessonContentDto.Materials)
</div>

<div class="lesson-section">
    <h2>Instructions</h2>
    @foreach (string instruction in LessonContentDto.Instructions)
    {
        @((MarkupString)instruction)
    }
</div>

<style>
    .lesson-section {
        margin-bottom: 20px;
    }

    h2 {
        color: var(--c4l-primary-purple);
        font-size: 22px;
        margin-bottom: 15px;
        font-weight: bold;
    }

    p {
        line-height: 1.6;
        margin-bottom: 15px;
    }

    ul {
        padding-left: 20px;
        margin-bottom: 15px;
    }

    li {
        margin-bottom: 10px;
        line-height: 1.6;
    }

    em {
        font-style: italic;
    }

    .section-divider {
        border-bottom: 1px dotted #ccc;
        margin: 30px 0;
        height: 1px;
    }

    .mark-complete-button {
        margin-top: 15px;
    }

    .preparation-completed {
        margin-top: 15px;
        color: var(--c4l-secondary-teal);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .completed-icon {
        font-size: 18px;
        font-weight: bold;
    }
</style>
