.deca-rating-summary-title {
    text-align: center;
    margin-block: 2rem 4rem;
    color: var(--c4l-primary-purple);
}

.roster-header {
    margin-bottom: 1.25rem;
}

.class-roster-title {
    color: var(--c4l-primary-purple);
    margin: 0;
    font-size: 1.5rem;
}

.search-input {
    padding: 0.5rem 0.75rem;
    border: 0.0625rem solid var(--neutral-300);
    border-radius: 0.25rem;
    width: 18.75rem;
    font-size: 1rem;
    transition:
      border-color var(--transition-speed) ease,
      opacity var(--transition-speed) ease;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        box-shadow: 0 0 0 0.125rem rgba(120, 53, 120, 0.25);
        opacity: 0;
        border-radius: 0.25rem;
        transition: opacity var(--transition-speed) ease;
    }

    &:hover {
        &::before {
            opacity: 1;
        }
    }

    &:focus {
        outline: none;
        border-color: var(--c4l-primary-purple);

        &::before {
            opacity: 1;
        }
    }
}

.class-roster {
    padding: 1.25rem;
}

.roster-row {
    border: 0.0625rem solid var(--neutral-200);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    background-color: var(--white);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.student-info-bar {
    padding: 0.75rem;
    cursor: pointer;
    background-color: var(--neutral-200);
    color: var(--neutral-500);
    border-radius: 0.25rem 0.25rem 0 0;
    transition:
      background-color var(--transition-speed) ease,
      color var(--transition-speed) ease;
}

.student-info-bar:hover {
    background-color: var(--neutral-500);
    color: var(--neutral-200);
}

.student-info-bar:hover .student-name {
    color: var(--neutral-200);
}

.student-info-bar:hover .view-child-btn {
    color: var(--neutral-200);
}

.student-info-bar:focus {
    outline: 0.125rem solid var(--white);
    outline-offset: -0.125rem;
}

.student-name {
    color: var(--neutral-500);
    margin: 0;
    transition: color var(--transition-speed) ease;
}

.view-child-btn {
    background-color: transparent;
    color: var(--neutral-500);
    border: 0.0625rem solid var(--neutral-300);
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition:
      background-color var(--transition-speed) ease,
      color var(--transition-speed) ease;
    gap: 0.5rem;
}

.student-info-bar .view-child-btn:hover {
    background-color: var(--neutral-100);
    color: var(--neutral-500);
}

.view-child-btn:focus {
    outline: 0.125rem solid var(--neutral-400);
    outline-offset: 0.125rem;
}

.rating-details {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-speed) ease-out;
}

.rating-details.expanded {
    max-height: 62.5rem;
    padding: 1rem;
    overflow-x: auto;
    overflow-y: hidden;
}

.ratings-grid-top {
    background-color: var(--neutral-200);
    color: var(--neutral-500);
    margin-top: 0.625rem;
    width: max-content;
    border: 0.0625rem solid var(--neutral-300);
    border-bottom: none;
}

.grid-cell {
    flex: 0 0 6.875rem;
    width: 6.875rem;
    padding: 0.375rem;
    text-align: center;
    border: 0.0625rem solid rgba(255, 255, 255, 0.2);
    font-size: 0.75rem;
    min-height: 3rem;
    color: var(--neutral-500);
    text-transform: uppercase;
    white-space: normal;
    overflow: visible;
    word-wrap: break-word;
}

.grid-group {
    flex: 0 0 14.0625rem;
    width: 14.0625rem;
    border-right: 0.0625rem solid rgba(255, 255, 255, 0.2);
}

.grid-group-title {
    padding: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    margin: 0;
    border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.2);
    background-color: var(--neutral-200);
    color: var(--neutral-500);
    text-transform: uppercase;
}

.grid-subgroup {
    flex: 1;
}

.grid-subcell {
    flex: 0 0 2.8125rem;
    width: 2.8125rem;
    padding: 0.25rem;
    text-align: center;
    border: 0.0625rem solid rgba(255, 255, 255, 0.2);
    font-size: 0.625rem;
    min-height: 2rem;
    color: var(--neutral-500);
    text-transform: uppercase;
    white-space: normal;
    overflow: visible;
    word-wrap: break-word;
}

.ratings-grid-content {
    width: max-content;
    border: 0.0625rem solid var(--neutral-300);
    border-top: none;
}

.rating-data-row {
    border-bottom: 0.0625rem solid var(--neutral-200);
    transition: background-color var(--transition-speed) ease;
}

.rating-data-row:hover {
    background-color: var(--neutral-100);
}

.rating-data-row:last-child {
    border-bottom: none;
}

.data-cell {
    flex: 0 0 6.875rem;
    width: 6.875rem;
    padding: 0.375rem;
    text-align: center;
    border-right: 0.0625rem solid var(--neutral-200);
    font-size: 0.75rem;
    min-height: 2.5rem;
    word-wrap: break-word;
    background-color: var(--white);
}

.data-group {
    flex: 0 0 14.0625rem;
    width: 14.0625rem;
    border-right: 0.0625rem solid var(--neutral-200);
}

.data-subcell {
    flex: 0 0 2.8125rem;
    width: 2.8125rem;
    padding: 0.25rem;
    text-align: center;
    border-right: 0.0625rem solid var(--neutral-200);
    font-size: 0.625rem;
    min-height: 2.5rem;
    word-wrap: break-word;
    background-color: var(--white);
}

.rating-actions {
    gap: 0.5rem;
}

@media (min-width: 48rem) {
    .deca-rating-summary-title {
        margin-block: 1rem 4rem;
    }

    .roster-header {
        gap: 1rem;
    }

    .search-input {
        width: 100%;
    }

    .grid-cell,
    .data-cell,
    .grid-subcell,
    .data-subcell {
        padding: 0.25rem;
        font-size: 0.625rem;
        min-height: 2rem;
    }

    .grid-group-title {
        font-size: 0.625rem;
        padding: 0.25rem;
    }

    .student-info-bar {
        gap: 0.5rem;
    }
}

@media (min-width: 75rem) {
    .grid-cell,
    .data-cell,
    .grid-subcell,
    .data-subcell {
        padding: 0.375rem;
        font-size: 0.75rem;
    }

    .grid-group-title {
        font-size: 0.75rem;
        padding: 0.375rem;
    }
}
