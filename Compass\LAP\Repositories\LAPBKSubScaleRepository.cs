using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class LAPBKSubScaleRepository : ILAPBKSubScaleRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public LAPBKSubScaleRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<LAPBKSubScale> SaveAsync(LAPBKSubScale lapbkSubScale)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (lapbkSubScale.Id == 0)
                {
                    // Create new record
                    _dbContext.LAPBKSubScales.Add(lapbkSubScale);
                }
                else
                {
                    // Update existing record
                    _dbContext.LAPBKSubScales.Update(lapbkSubScale);
                }

                await _dbContext.SaveChangesAsync();
                return lapbkSubScale;
            }
        }

        public async Task<LAPBKSubScale?> GetByIdAsync(long? organizationId, long? id, long? assessmentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.LAPBKSubScales
                    .FirstOrDefaultAsync(l => l.Id == id.Value && l.OrganizationId == organizationId.Value && l.AssessmentId == assessmentId.Value);
            }
        }
    }
}
