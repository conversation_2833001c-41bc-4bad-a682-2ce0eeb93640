﻿@page "/lap-guided-entry"
@using Microsoft.AspNetCore.Authorization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="container-fluid p-0">
    <!-- Breadcrumb and Header as List Boxes -->
    <div class="bg-white border-bottom px-4 py-2 d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <select class="form-select form-select-sm me-2" style="width: 180px;">
                <option selected>Cantsee Classroom</option>
                <option>Other Classroom</option>
            </select>
            <select class="form-select form-select-sm me-2" style="width: 120px;">
                <option selected>Beginning</option>
                <option>Middle</option>
                <option>End</option>
            </select>
            <select class="form-select form-select-sm me-2" style="width: 100px;">
                <option selected>GM</option>
                <option>FM</option>
                <option>LM</option>
            </select>
            <span class="fw-bold ms-2">E-LAP</span>
        </div>
    </div>
    <div class="px-4 pt-3 pb-2">
        <span class="text-success fw-bold">SECTION SCORING</span>
    </div>

    <!-- Main Content -->
    <div class="px-4">
        <div class="row mb-3">
            <!-- Assessment Details -->
            <div class="col-12 d-flex align-items-center border rounded bg-white p-3">
                <div class="me-5">
                    <div class="text-secondary">Age</div>
                    <div class="display-6 fw-bold">68</div>
                </div>
                <div class="me-5">
                    <div class="text-secondary">Basal</div>
                    <div>Hops On One Foot-- 2 Or More Hops</div>
                </div>
                <div class="me-5">
                    <div class="text-secondary">Materials</div>
                    <div>Stick or broom handle</div>
                </div>
                <div class="me-5">
                    <div class="text-secondary">Ceiling</div>
                    <div></div>
                </div>
                <div class="ms-auto d-flex align-items-center">
                    <div class="me-3">
                        <div class="text-secondary">Date</div>
                        <input type="date" class="form-control form-control-sm" value="2025-06-06" style="width: 130px;" />
                    </div>
                    <div class="me-3">
                        <div class="text-secondary">Score</div>
                        <button class="btn btn-outline-success btn-sm me-1"><i class="bi bi-plus-circle"></i></button>
                        <button class="btn btn-outline-danger btn-sm"><i class="bi bi-dash-circle"></i></button>
                    </div>
                    <button class="btn btn-warning btn-sm me-2"><i class="bi bi-pencil-square"></i></button>
                    <button class="btn btn-warning btn-sm me-2"><i class="bi bi-calendar-event"></i></button>
                    <button class="btn btn-primary btn-sm">Finished</button>
                </div>
            </div>
        </div>

        <!-- Item Details -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex align-items-center bg-white border rounded p-3">
                    <div class="me-4">
                        <div class="display-6 fw-bold">GM88</div>
                    </div>
                    <div>
                        <div class="fw-bold">Hops On One Foot-- 2 Or More Hops</div>
                        <div class="text-secondary small">Stick or broom handle</div>
                    </div>
                    <div class="ms-auto">
                        <div class="text-secondary small">Scoring Criteria</div>
                        <div>The child will hop 2-3 times without putting down the other foot.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Item List Table -->
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-bordered mb-0">
                        <thead class="table-primary">
                            <tr>
                                <th>Item</th>
                                <th>Dev Age</th>
                                <th>Description</th>
                                <th>Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>GM77</td>
                                <td>18</td>
                                <td>Runs</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM78</td>
                                <td>18</td>
                                <td>Pushes And Pulls Large Objects</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM79</td>
                                <td>18</td>
                                <td>Throws Ball Overhand Without Falling</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM80</td>
                                <td>19</td>
                                <td>Carries Large Teddy Bear or Doll While Walking</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM81</td>
                                <td>21</td>
                                <td>Creeps Backward Down Stairs</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM82</td>
                                <td>21</td>
                                <td>Walks With One Foot On Walking Board</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM83</td>
                                <td>24</td>
                                <td>Jumps In Place</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM84</td>
                                <td>24</td>
                                <td>Walks Approximately On Line</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM85</td>
                                <td>24</td>
                                <td>Jumps From Bottom Step</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM86</td>
                                <td>28</td>
                                <td>Walks Backwards</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>GM87</td>
                                <td>30</td>
                                <td>Stands Up From Supine</td>
                                <td></td>
                            </tr>
                            <tr style="background-color: orange; color: white; font-weight: bold;">
                                <td>GM88</td>
                                <td>36</td>
                                <td>Hops On One Foot-- 2 Or More Hops</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
