using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Interfaces.Repositories
{
    public interface ILAP3ItemRepository
    {
        Task<LAP3Item> SaveAsync(LAP3Item lap3Item);
        Task<List<LAP3Item>> GetItemsBySubscaleAsync(long? organizationId, long? assessmentId, long? subscaleId);
        Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId, long? subscaleId);
    }
}
