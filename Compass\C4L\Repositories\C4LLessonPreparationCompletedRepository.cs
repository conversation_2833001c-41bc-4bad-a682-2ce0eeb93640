using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class C4LLessonPreparationCompletedRepository : IC4LLessonPreparationCompletedRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public C4LLessonPreparationCompletedRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }
        private void ValidateProperties(C4LLessonPreparationCompleted completedPreparation)
        {
            // Ensure organization ID is set
            if (completedPreparation.OrganizationId <= 0)
            {
                throw new ArgumentException("Organization ID must be set", nameof(completedPreparation));
            }

            // Ensure C4L Classroom ID is set
            if (completedPreparation.C4LClassroomId <= 0)
            {
                throw new ArgumentException("C4L classroom ID must be set", nameof(completedPreparation));
            }

            // Ensure Unit is set
            if (completedPreparation.Unit <= 0)
            {
                throw new ArgumentException("Unit must be set", nameof(completedPreparation));
            }

            // Ensure Week is set
            if (completedPreparation.Week <= 0)
            {
                throw new ArgumentException("Week must be set", nameof(completedPreparation));
            }

            // Ensure Day is set
            if (completedPreparation.Day <= 0)
            {
                throw new ArgumentException("Day must be set", nameof(completedPreparation));
            }

            // Ensure LessonTypeSequence is set
            if (completedPreparation.LessonTypeSequence <= 0)
            {
                throw new ArgumentException("Lesson type sequence must be set", nameof(completedPreparation));
            }

            // Ensure TitleSequence is set
            if (completedPreparation.TitleSequence <= 0)
            {
                throw new ArgumentException("Title sequence must be set", nameof(completedPreparation));
            }

        }

        public async Task<C4LLessonPreparationCompleted?> GetByAsync(C4LLessonPreparationCompleted completedPreparation)
        {
            ValidateProperties(completedPreparation);

            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                return await dbContext.C4LLessonPreparationCompleteds
                    .FirstOrDefaultAsync(c => c.OrganizationId == completedPreparation.OrganizationId 
                                            && c.Schoolyear == completedPreparation.Schoolyear
                                            && c.C4LClassroomId == completedPreparation.C4LClassroomId
                                            && c.Unit == completedPreparation.Unit
                                            && c.Week == completedPreparation.Week
                                            && c.Day == completedPreparation.Day
                                            && c.LessonTypeSequence == completedPreparation.LessonTypeSequence
                                            && c.TitleSequence == completedPreparation.TitleSequence);
            }
        }

        public async Task<C4LLessonPreparationCompleted> CreateAsync(C4LLessonPreparationCompleted completedPreparation)
        {
            ValidateProperties(completedPreparation);

            // Get the current user ID for the mod_id field
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier) ?? string.Empty;

            // Set the ModId and ModTs
            completedPreparation.ModId = userId;
            completedPreparation.ModTs = DateTime.Now;

            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                await dbContext.C4LLessonPreparationCompleteds.AddAsync(completedPreparation);
                await dbContext.SaveChangesAsync();
                return completedPreparation;
            }
        }

        public async Task<C4LLessonPreparationCompleted> UpdateAsync(C4LLessonPreparationCompleted completedPreparation)
        {
            ValidateProperties(completedPreparation);

            // Ensure ID is set
            if (completedPreparation.Id <= 0)
            {
                throw new ArgumentException("ID must be set", nameof(completedPreparation));
            }

            // Get the current user ID for the mod_id field
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier) ?? string.Empty;

            // Update ModId and ModTs
            completedPreparation.ModId = userId;
            completedPreparation.ModTs = DateTime.Now;

            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                dbContext.C4LLessonPreparationCompleteds.Update(completedPreparation);
                await dbContext.SaveChangesAsync();
                return completedPreparation;
            }
        }

        public async Task<C4LLessonPreparationCompleted?> UpdateCompletionStatusAsync(C4LLessonPreparationCompleted completedPreparation, bool isCompleted)
        {
            // Get the current user ID for the mod_id field
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier) ?? string.Empty;

            C4LLessonPreparationCompleted? existingPrepCompletion = null;
            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                existingPrepCompletion = await dbContext.C4LLessonPreparationCompleteds
                    .FirstOrDefaultAsync(c => c.Id == completedPreparation.Id);
                if (existingPrepCompletion == null)
                {
                    throw new ArgumentException($"No preparation completed with ID {completedPreparation.Id} found");
                }
            }

            if (existingPrepCompletion != null )
            {
                // Update ModId and ModTs
                existingPrepCompletion.ModId = userId;
                existingPrepCompletion.ModTs = DateTime.Now;
                existingPrepCompletion.DateCompleted = isCompleted ? DateTime.Now : null;

                using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
                {
                    dbContext.C4LLessonPreparationCompleteds.Update(existingPrepCompletion);
                    await dbContext.SaveChangesAsync();
                }
            }

            return existingPrepCompletion;
        }
    }
}
