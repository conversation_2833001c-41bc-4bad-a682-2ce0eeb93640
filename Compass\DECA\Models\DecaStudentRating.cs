﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Deca.Models
{
    [Table("edeca_student_ratings")]
    public class DecaStudentRating
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Column("mod_id")]
        [MaxLength(450)]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("student_id")]
        [Required]
        public long StudentId { get; set; }

        [Column("status")]
        [MaxLength(1)]
        public char? Status { get; set; }

        [Column("rating_date")]
        public DateTime? RatingDate { get; set; }

        [Column("rating_Year")]
        [Required]
        public int RatingYear { get; set; }

        [Column("rating_period")]
        [Required]
        [MaxLength(5)]
        public string RatingPeriod { get; set; } = string.Empty;

        [Column("record_form")]
        [MaxLength(10)]
        public string? RecordForm { get; set; }

        [Column("rating_level")]
        [MaxLength(10)]
        public string? RatingLevel { get; set; }

        [Column("rater_id")]
        [MaxLength(450)]
        public string? RaterId { get; set; }

        [Column("rater_type")]
        [Required]
        [MaxLength(10)]
        public string RaterType { get; set; } = string.Empty;

        [Column("age_at_rating_months")]
        public int? AgeAtRatingMonths { get; set; }

        // IN (Inattention) Scale
        [Column("in_raw")]
        public int? InRaw { get; set; }

        [Column("in_tscore")]
        public int? InTScore { get; set; }

        [Column("in_percentile")]
        public int? InPercentile { get; set; }

        [Column("in_description")]
        [MaxLength(5)]
        public string? InDescription { get; set; }

        // SC (Self-Control) Scale
        [Column("sc_raw")]
        public int? ScRaw { get; set; }

        [Column("sc_tscore")]
        public int? ScTScore { get; set; }

        [Column("sc_percentile")]
        public int? ScPercentile { get; set; }

        [Column("sc_description")]
        [MaxLength(5)]
        public string? ScDescription { get; set; }

        // AT (Attention) Scale
        [Column("ar_raw")]
        public int? ArRaw { get; set; }

        [Column("ar_tscore")]
        public int? ArTScore { get; set; }

        [Column("ar_percentile")]
        public int? ArPercentile { get; set; }

        [Column("ar_description")]
        [MaxLength(5)]
        public string? AtDescription { get; set; }

        // TPF (Task Planning/Focus) Scale
        [Column("tpf_raw")]
        public int? TpfRaw { get; set; }

        [Column("tpf_tscore")]
        public int? TpfTScore { get; set; }

        [Column("tpf_percentile")]
        public int? TpfPercentile { get; set; }

        [Column("tpf_description")]
        [MaxLength(5)]
        public string? TpfDescription { get; set; }

        // BC (Behavioral Control) Scale
        [Column("bc_raw")]
        public int? BcRaw { get; set; }

        [Column("bc_tscore")]
        public int? BcTScore { get; set; }

        [Column("bc_percentile")]
        public int? BcPercentile { get; set; }

        [Column("bc_description")]
        [MaxLength(5)]
        public string? BcDescription { get; set; }

        // PR (Peer Relations) Scale
        [Column("pr_raw")]
        public int? PrRaw { get; set; }

        [Column("pr_tscore")]
        public int? PrTScore { get; set; }

        [Column("pr_percentile")]
        public int? PrPercentile { get; set; }

        [Column("pr_description")]
        [MaxLength(5)]
        public string? PrDescription { get; set; }

        // OT (Organization/Time Management) Scale
        [Column("ot_raw")]
        public int? OtRaw { get; set; }

        [Column("ot_tscore")]
        public int? OtTScore { get; set; }

        [Column("ot_percentile")]
        public int? OtPercentile { get; set; }

        [Column("ot_description")]
        [MaxLength(5)]
        public string? OtDescription { get; set; }

        // GB (Goal-directed Behavior) Scale
        [Column("gb_raw")]
        public int? GbRaw { get; set; }

        [Column("gb_tscore")]
        public int? GbTScore { get; set; }

        [Column("gb_percentile")]
        public int? GbPercentile { get; set; }

        [Column("gb_description")]
        [MaxLength(5)]
        public string? GbDescription { get; set; }

        // SO (Social) Scale
        [Column("so_raw")]
        public int? SoRaw { get; set; }

        [Column("so_tscore")]
        public int? SoTScore { get; set; }

        [Column("so_percentile")]
        public int? SoPercentile { get; set; }

        [Column("so_description")]
        [MaxLength(5)]
        public string? SoDescription { get; set; }

        // DM (Decision Making) Scale
        [Column("dm_raw")]
        public int? DmRaw { get; set; }

        [Column("dm_tscore")]
        public int? DmTScore { get; set; }

        [Column("dm_percentile")]
        public int? DmPercentile { get; set; }

        [Column("dm_description")]
        [MaxLength(5)]
        public string? DmDescription { get; set; }

        // RS (Response to Stress) Scale
        [Column("rs_raw")]
        public int? RsRaw { get; set; }

        [Column("rs_tscore")]
        public int? RsTScore { get; set; }

        [Column("rs_percentile")]
        public int? RsPercentile { get; set; }

        [Column("rs_description")]
        [MaxLength(5)]
        public string? RsDescription { get; set; }

        // SA (Social Awareness) Scale
        [Column("sa_raw")]
        public int? SaRaw { get; set; }

        [Column("sa_tscore")]
        public int? SaTScore { get; set; }

        [Column("sa_percentile")]
        public int? SaPercentile { get; set; }

        [Column("sa_description")]
        [MaxLength(5)]
        public string? SaDescription { get; set; }

        // SM (Self-Management) Scale
        [Column("sm_raw")]
        public int? SmRaw { get; set; }

        [Column("sm_tscore")]
        public int? SmTScore { get; set; }

        [Column("sm_percentile")]
        public int? SmPercentile { get; set; }

        [Column("sm_description")]
        [MaxLength(5)]
        public string? SmDescription { get; set; }

        // SEC (Social-Emotional Composite) Scale
        [Column("sec_raw")]
        public int? SecRaw { get; set; }

        [Column("sec_tscore")]
        public int? SecTScore { get; set; }

        [Column("sec_percentile")]
        public int? SecPercentile { get; set; }

        [Column("sec_description")]
        [MaxLength(5)]
        public string? SecDescription { get; set; }

        [Column("origin_date")]
        public DateTime? OriginDate { get; set; }
    }
}