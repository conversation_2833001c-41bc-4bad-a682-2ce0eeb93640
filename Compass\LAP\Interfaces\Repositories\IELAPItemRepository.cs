using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Interfaces.Repositories
{
    public interface IELAPItemRepository
    {
        Task<ELAPItem> SaveAsync(ELAPItem elapItem);
        Task<List<ELAPItem>> GetItemsBySubscaleAsync(long? organizationId, long? assessmentId, long? subscaleId);
        Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId, long? subscaleId);
    }
}
