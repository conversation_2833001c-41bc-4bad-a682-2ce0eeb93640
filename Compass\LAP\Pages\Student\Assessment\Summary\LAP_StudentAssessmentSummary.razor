﻿@page "/lap-student-assessment-summary"
@using Compass.LAP.Models
@using Compass.LAP.DTOs

<h1 class="page-title horizontal-line assessment-summary-title">Progress Summary</h1>

<div class="assessment-summary-wrapper">
    <div class="assessment-summary">

        <div class="info-legend-container">
            <div class="student-info">
                <p class="student-name">Student: @(string.IsNullOrWhiteSpace(studentName) ? "Unknown Student" : studentName)</p>
                <p class="student-dob">Date of Birth: @(studentBirthdate?.ToShortDateString())</p>
            </div>

            <div class="progress-legend" role="group" aria-labelledby="legend-heading">
                <h2 id="legend-heading" class="visually-hidden">Assessment Status Legend</h2>

                <div class="legend-item">
                    <div class="legend-box completed" aria-hidden="true"></div>
                    <span>Completed</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box started" aria-hidden="true"></div>
                    <span>Started</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box invalid" aria-hidden="true"></div>
                    <span>Invalid</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box low-scores" aria-hidden="true"></div>
                    <span>Low Z Scores</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box no-item" aria-hidden="true"></div>
                    <span>No Items</span>
                </div>
            </div>
        </div>

        <section class="assessment-table" role="grid" aria-label="Student Assessment Progress Summary">
            <p class="visually-hidden">
                Assessment scores organized by checkpoint and subject area.
                E-LAP covers 6 subjects, LAP-D covers 13 subjects.
                Scores are color-coded by status: completed assessments, started but incomplete assessments,
                invalid assessments, low Z-scores (future feature), and areas with no items available.
            </p>

            <div class="table-header">
                <div class="checkpoint-column">
                    <div class="header-cell checkpoint-header">Checkpoint</div>
                </div>
                <div class="elap-section">
                    <div class="section-header">E-LAP</div>
                    <div class="subheader-row">
                        <div class="subheader-cell">GM</div>
                        <div class="subheader-cell">FM</div>
                        <div class="subheader-cell">CG</div>
                        <div class="subheader-cell">LN</div>
                        <div class="subheader-cell">SH</div>
                        <div class="subheader-cell">SE</div>
                    </div>
                </div>
                <div class="lapd-section">
                    <div class="section-header">LAP-D</div>
                    <div class="subheader-row">
                        <div class="subheader-cell">FM</div>
                        <div class="subheader-cell">FW</div>
                        <div class="subheader-cell">CM</div>
                        <div class="subheader-cell">CC</div>
                        <div class="subheader-cell">LN</div>
                        <div class="subheader-cell">LC</div>
                        <div class="subheader-cell">GB</div>
                        <div class="subheader-cell">GD</div>
                        <div class="subheader-cell">PS</div>
                        <div class="subheader-cell">SH</div>
                        <div class="subheader-cell">DB</div>
                        <div class="subheader-cell">DE</div>
                        <div class="subheader-cell">DL</div>
                    </div>
                </div>
            </div>

            @if (ElapAssessmentContainerList != null)
            {
                @foreach (LapAssessmentContainerDto assessment in ElapAssessmentContainerList)
                {
                    List<LapSubscaleContainerDto> subscaleList = assessment?.LapSubscaleContainerList ?? new List<LapSubscaleContainerDto>();
                    int elapCount = Math.Min(ELAP_COLUMN_COUNT, subscaleList.Count);

                <div class="assessment-row">
                    <div class="date-row">
                        <div class="checkpoint-column">
                        </div>
                        <div class="elap-section">
                            <div class="date-cell elap-date">@FormatAssessmentDate(assessment?.AssessmentDate)</div>
                        </div>
                        @if (subscaleList.Count > ELAP_COLUMN_COUNT)
                        {
                            <div class="lapd-section">
                                <div class="date-cell lapd-date">@FormatAssessmentDate(assessment?.AssessmentDate)</div>
                            </div>
                        }
                    </div>

                    <div class="data-row">
                        <div class="checkpoint-column">
                            <div class="checkpoint-cell">@GetCheckpointName(assessment?.Checkpoint ?? 0)</div>
                        </div>
                        <div class="elap-section">
                            <div class="score-row">
                                @for (int i = 0; i < ELAP_COLUMN_COUNT; i++)
                                {
                                    @if (i < subscaleList.Count)
                                    {
                                        var subscale = subscaleList[i];
                                        @if (subscale != null)
                                        {
                                            <button class="score-button @GetScoreClass(subscale)"
                                                    aria-label="@GetCheckpointName(assessment?.Checkpoint ?? 0) score: @(subscale.RawScore?.ToString("0.00") ?? "No score"), Status: @GetScoreClass(subscale)">
                                                @if (subscale.RawScore.HasValue)
                                                {
                                                    @subscale.RawScore?.ToString("0.00")
                                                }
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="score-button no-item"
                                                    aria-label="@GetCheckpointName(assessment?.Checkpoint ?? 0): No items available">
                                            </button>
                                        }
                                    }
                                    else
                                    {
                                        <button class="score-button no-item"
                                                aria-label="@GetCheckpointName(assessment?.Checkpoint ?? 0): No items available">
                                        </button>
                                    }
                                }
                            </div>
                        </div>
                        <div class="lapd-section">
                            <div class="score-row">
                                @for (int i = ELAP_COLUMN_COUNT; i < TOTAL_COLUMN_COUNT; i++)
                                {
                                    @if (i < subscaleList.Count)
                                    {
                                        var subscale = subscaleList[i];
                                        @if (subscale != null)
                                        {
                                            <button class="score-button @GetScoreClass(subscale)"
                                                    aria-label="@GetCheckpointName(assessment?.Checkpoint ?? 0) score: @(subscale.RawScore?.ToString("0.00") ?? "No score"), Status: @GetScoreClass(subscale)">
                                                @if (subscale.RawScore.HasValue)
                                                {
                                                    @subscale.RawScore?.ToString("0.00")
                                                }
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="score-button no-item"
                                                    aria-label="@GetCheckpointName(assessment?.Checkpoint ?? 0): No items available">
                                            </button>
                                        }
                                    }
                                    else
                                    {
                                        <button class="score-button no-item"
                                                aria-label="@GetCheckpointName(assessment?.Checkpoint ?? 0): No items available">
                                        </button>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                </div>
                }
            }
        </section>
    </div>
</div>