﻿using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.DTOs
{
    public class LapAssessmentEntryDto
    {
        public SmartLapAssessment? SmartAssessment { get; set; }
        public List<Milestone> LapStaticItems { get; set; } = new List<Milestone>();
        public List<Milestone> LapStaticSubscales { get; set; } = new List<Milestone>();
        public long? CurrentSubscaleInstID { get; set; }
    }
}
