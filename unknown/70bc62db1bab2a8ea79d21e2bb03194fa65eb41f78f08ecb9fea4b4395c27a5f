﻿using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class StaticItemRepository : IStaticItemRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public StaticItemRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<List<LAPItemsLookup>> GetStaticItems(int? instrument, int? language, long? staticSubscaleId)
        {
            if (instrument is null)
            {
                throw new ArgumentNullException(nameof(instrument));
            }

            if (language is null)
            {
                throw new ArgumentNullException(nameof(language));
            }

            if (staticSubscaleId is null)
            {
                throw new ArgumentNullException(nameof(staticSubscaleId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<LAPItemsLookup> items = await _dbContext.LAPItemsLookups
                    .Where(i => i.Instrument == instrument.Value
                             && i.Language == language.Value
                             && i.SubscaleStaticId == staticSubscaleId.Value)
                    .OrderBy(i => i.Sequence)
                    .ToListAsync();

                return items;
            }
        }
    }
}
