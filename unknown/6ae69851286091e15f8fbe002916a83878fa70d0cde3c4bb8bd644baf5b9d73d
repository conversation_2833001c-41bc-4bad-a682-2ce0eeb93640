@page "/lap-session-test"
@using Microsoft.AspNetCore.Authorization
@inject ILAPSessionStateService LAPSessionService
@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<PageTitle>LAP Session Test</PageTitle>

<h3>LAP Session State Test</h3>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Set Assessment ID</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="assessmentId" class="form-label">Assessment ID:</label>
                    <input type="number" class="form-control" id="assessmentId" @bind="newAssessmentId" />
                </div>
                <button class="btn btn-primary" @onclick="SetAssessmentIdAsync">Set Assessment ID</button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Current Session Data</h5>
            </div>
            <div class="card-body">
                <p><strong>Assessment ID:</strong> @currentAssessmentId</p>
                <button class="btn btn-secondary" @onclick="RefreshDataAsync">Refresh Data</button>
                <button class="btn btn-danger" @onclick="ClearDataAsync">Clear Session</button>
            </div>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(statusMessage))
{
    <div class="alert alert-info mt-3">
        @statusMessage
    </div>
}

@code {
    private long newAssessmentId = 0;
    private long currentAssessmentId = 0;
    private string statusMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await RefreshDataAsync();
    }

    private async Task SetAssessmentIdAsync()
    {
        try
        {
            Models.LAPSessionData sessionData = new Models.LAPSessionData
            {
                AssessmentId = newAssessmentId
            };

            await LAPSessionService.SetSessionDataAsync(sessionData);
            await RefreshDataAsync();
            statusMessage = $"Assessment ID set to {newAssessmentId}";
        }
        catch (Exception ex)
        {
            statusMessage = $"Error setting assessment ID: {ex.Message}";
        }
    }

    private async Task RefreshDataAsync()
    {
        try
        {
            Models.LAPSessionData sessionData = await LAPSessionService.GetSessionDataAsync();
            currentAssessmentId = sessionData.AssessmentId;
            statusMessage = "Data refreshed successfully";
        }
        catch (Exception ex)
        {
            statusMessage = $"Error refreshing data: {ex.Message}";
        }
    }

    private async Task ClearDataAsync()
    {
        try
        {
            await LAPSessionService.ClearSessionDataAsync();
            await RefreshDataAsync();
            statusMessage = "Session data cleared";
        }
        catch (Exception ex)
        {
            statusMessage = $"Error clearing data: {ex.Message}";
        }
    }
}
