using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_progress_observation")]
    public class ProgressObservation
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Required]
        [Column("customerInstID")]
        public long OrganizationId { get; set; }

        [Column("observerInstID")]
        public string? ObserverId { get; set; }

        [Required]
        [Column("dateOfObservation")]
        public DateTime DateOfObservation { get; set; }

        [Required]
        [Column("childInstID")]
        public long StudentId { get; set; }

        [Column("note")]
        public string? Note { get; set; }

        [Column("uploadedFileInstID")]
        public long? UploadedFileId { get; set; }

        [Column("CrossDomainScoreStaticId")]
        public long? CrossDomainScoreStaticId { get; set; }

        [Column("schoolYear")]
        public int? SchoolYear { get; set; }
    }
}
