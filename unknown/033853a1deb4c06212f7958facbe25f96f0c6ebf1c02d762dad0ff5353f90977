using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Interfaces.Repositories
{
    public interface ILAPScreenItemRepository
    {
        Task<LAPScreenItem> SaveAsync(LAPScreenItem lapScreenItem);
        Task<List<LAPScreenItem>> GetItemsByAssessmentAsync(long? organizationId, long? assessmentId);
        Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId);
    }
}
