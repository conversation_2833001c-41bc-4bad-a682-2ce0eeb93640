﻿using Compass.LAP.DTOs;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Interfaces.Services;
using Compass.LAP.Models;
using Compass.LAP.Resources.Instruments;
using Compass.LAP.Resources.SmartAssessment.Criterion;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Services
{
    public class LapAssessmentService : ILapAssessmentService
    {
        private readonly IStaticSubscaleRepository _staticSubscaleRepository;
        private readonly IStaticItemRepository _staticItemRepository;
        private readonly IAssessmentRepository _assessmentRepository;
        private readonly IELAPSubScaleRepository _elapSubScaleRepository;
        private readonly ILAP3SubScaleRepository _lap3SubScaleRepository;
        private readonly ILAPBKSubScaleRepository _lapBKSubScaleRepository;
        private readonly ILAPD3SubScaleRepository _lapD3SubScaleRepository;
        private readonly IELAPItemRepository _elapItemRepository;
        private readonly ILAP3ItemRepository _lap3ItemRepository;
        private readonly ILAPBKItemRepository _lapBKItemRepository;
        private readonly ILAPD3ItemRepository _lapD3ItemRepository;
        private readonly ILAPScreenItemRepository _lapScreenItemRepository;

        public LapAssessmentService(IStaticSubscaleRepository staticSubscaleRepository,
                                    IStaticItemRepository staticItemRepository,
                                    IAssessmentRepository assessmentRepository,
                                    IELAPSubScaleRepository elapSubScaleRepository,
                                    ILAP3SubScaleRepository lap3SubScaleRepository,
                                    ILAPBKSubScaleRepository lapBKSubScaleRepository,
                                    ILAPD3SubScaleRepository lapD3SubScaleRepository,
                                    IELAPItemRepository elapItemRepository,
                                    ILAP3ItemRepository lap3ItemRepository,
                                    ILAPBKItemRepository lapBKItemRepository,
                                    ILAPD3ItemRepository lapD3ItemRepository,
                                    ILAPScreenItemRepository lapScreenItemRepository)
        {
            _staticSubscaleRepository = staticSubscaleRepository;
            _staticItemRepository = staticItemRepository;
            _assessmentRepository = assessmentRepository;
            _elapSubScaleRepository = elapSubScaleRepository;
            _lap3SubScaleRepository = lap3SubScaleRepository;
            _lapBKSubScaleRepository = lapBKSubScaleRepository;
            _lapD3SubScaleRepository = lapD3SubScaleRepository;
            _elapItemRepository = elapItemRepository;
            _lap3ItemRepository = lap3ItemRepository;
            _lapBKItemRepository = lapBKItemRepository;
            _lapD3ItemRepository = lapD3ItemRepository;
            _lapScreenItemRepository = lapScreenItemRepository;
        }

        private async Task<List<Milestone>> GetStaticSubscales(int? instrument, int? language)
        {
            List<LAPSubScalesLookup> staticSubscales = await _staticSubscaleRepository.GetStaticSubscales(instrument, language);

            List<Milestone> ret = new List<Milestone>();
            foreach (LAPSubScalesLookup lapSubscalesLookup in staticSubscales)
            {
                Milestone milestone = new Milestone();

                milestone.Domain = lapSubscalesLookup.Domain;
                milestone.DomainSequence = lapSubscalesLookup.DomainSequence;
                milestone.Instrument = lapSubscalesLookup.Instrument;
                milestone.SubscaleStaticID = lapSubscalesLookup.StaticId;
                milestone.SubscaleID = lapSubscalesLookup.SubscaleId;
                milestone.SubscaleSequence = lapSubscalesLookup.SubscaleSequence;
                milestone.Language = lapSubscalesLookup.Language;

                ret.Add(milestone);
            }
            return ret;
        }

        private Milestone GetCurrentStaticSubscale(long? StaticSubscaleId, List<Milestone> staticSubscales)
        {
            Milestone? ret = null;

            foreach (Milestone lapSubscalesLookup in staticSubscales)
            {
                if (lapSubscalesLookup.SubscaleStaticID == StaticSubscaleId)
                {
                    ret = lapSubscalesLookup;
                    break;
                }
            }

            if (ret == null)
            {
                throw new Exception("No Static Subscale Found");
            }

            return ret;
        }

        private async Task<List<Milestone>> GetStaticItemList(int? instrument, int? language, long? staticSubscaleId, int? subscaleSequence)
        {
            List<LAPItemsLookup> staticItems = await _staticItemRepository.GetStaticItems(instrument, language, staticSubscaleId);

            List<Milestone> ret = new List<Milestone>();
            foreach (LAPItemsLookup lapItemLookup in staticItems)
            {
                Milestone milestone = new Milestone();

                milestone.CollectionMethods = lapItemLookup.CollectionMethods;
                milestone.Comment = lapItemLookup.Comment;
                milestone.Description = lapItemLookup.Description;
                milestone.Instrument = lapItemLookup.Instrument;
                milestone.Language = lapItemLookup.Language;
                milestone.Materials = lapItemLookup.Materials;
                milestone.MinSelectionRequired = lapItemLookup.MinSelectionRequired;
                milestone.Months = lapItemLookup.Months;
                milestone.Sequence = lapItemLookup.Sequence;
                milestone.Skill = lapItemLookup.Skill;
                milestone.SubscaleID = lapItemLookup.SubscaleId;
                milestone.ItemStaticID = lapItemLookup.StaticId;
                milestone.ItemID = lapItemLookup.SubscaleId + lapItemLookup.Sequence;
                milestone.SubscaleSequence = subscaleSequence;
                milestone.ItemNo = lapItemLookup.ItemNo;
                milestone.SubscaleStaticID = staticSubscaleId;
                milestone.Procedure = lapItemLookup.ItemProcedure;

                ret.Add(milestone);
            }

            return ret;
        }

        private SmartLapAssessment? CreateSmartAssessment(int? instrument, int? lapCheckptInt)
        {
            SmartLapAssessment? smartAssessment = SmartLapAssessment.Create(instrument);

            if (smartAssessment is CriterionAssessment criterionAssessment)
            {
                Checkpoint? checkpoint = CheckpointExtensions.GetCheckpoint(lapCheckptInt, instrument);
                criterionAssessment.AssessmentCheckpoint = checkpoint;

                // Create empty subscale list
                List<Subscale> subscaleList = new List<Subscale>();
                criterionAssessment.Subscales = subscaleList;
            }

            return smartAssessment;
        }

        private async Task<Subscale?> GetELapSubscale(long? organizationId, long? assessmentId, long? subscaleId)
        {
            ELAPSubScale? elapSubscale = await _elapSubScaleRepository.GetByIdAsync(organizationId, subscaleId, assessmentId);

            if (elapSubscale == null)
            {
                return null;
            }

            Subscale subscale = new Subscale();
            subscale.Id = subscaleId;
            subscale.AssessmentInstID = elapSubscale.AssessmentId;
            subscale.Basal = elapSubscale.Basal;
            subscale.Ceiling = elapSubscale.Ceiling;
            subscale.ChronologicalAge = elapSubscale.ChronologicalAge;
            subscale.DateOfAssessment = elapSubscale.DateAssessed;
            subscale.Domain = elapSubscale.LapDomain;
            subscale.RawScore = elapSubscale.RawScore;
            subscale.SubscaleStaticID = elapSubscale.SubscaleStaticId;
            subscale.Instrument = AssessmentLevel.E_LAP;

            return subscale;
        }

        private async Task<Subscale?> GetLap3Subscale(long? organizationId, long? assessmentId, long? subscaleId)
        {
            LAP3SubScale? lap3Subscale = await _lap3SubScaleRepository.GetByIdAsync(organizationId, subscaleId, assessmentId);

            if (lap3Subscale == null)
            {
                return null;
            }

            Subscale subscale = new Subscale();
            subscale.Id = subscaleId;
            subscale.AssessmentInstID = lap3Subscale.AssessmentId;
            subscale.Basal = lap3Subscale.Basal;
            subscale.Ceiling = lap3Subscale.Ceiling;
            subscale.ChronologicalAge = lap3Subscale.ChronologicalAge;
            subscale.DateOfAssessment = lap3Subscale.DateAssessed;
            subscale.Domain = lap3Subscale.LapDomain;
            subscale.RawScore = lap3Subscale.RawScore;
            subscale.SubscaleStaticID = lap3Subscale.SubscaleStaticId;
            subscale.Instrument = AssessmentLevel.LAP_3;

            return subscale;
        }

        private async Task<Subscale?> GetLapBKSubscale(long? organizationId, long? assessmentId, long? subscaleId)
        {
            LAPBKSubScale? lapBKSubscale = await _lapBKSubScaleRepository.GetByIdAsync(organizationId, subscaleId, assessmentId);

            if (lapBKSubscale == null)
            {
                return null;
            }

            Subscale subscale = new Subscale();
            subscale.Id = subscaleId;
            subscale.AssessmentInstID = lapBKSubscale.AssessmentId;
            subscale.Basal = lapBKSubscale.Basal;
            subscale.Ceiling = lapBKSubscale.Ceiling;
            subscale.ChronologicalAge = lapBKSubscale.ChronologicalAge;
            subscale.DateOfAssessment = lapBKSubscale.DateAssessed;
            subscale.Domain = lapBKSubscale.LapDomain;
            subscale.RawScore = lapBKSubscale.RawScore;
            subscale.SubscaleStaticID = lapBKSubscale.SubscaleStaticId;
            subscale.Instrument = AssessmentLevel.LAP_BK;

            return subscale;
        }

        private async Task<Subscale?> GetLapD3Subscale(long? organizationId, long? assessmentId, long? subscaleId)
        {
            LAPD3SubScale? lapD3Subscale = await _lapD3SubScaleRepository.GetByIdAsync(organizationId, subscaleId, assessmentId);

            if (lapD3Subscale == null)
            {
                return null;
            }

            Subscale subscale = new Subscale();
            subscale.Id = subscaleId;
            subscale.AssessmentInstID = lapD3Subscale.AssessmentId;
            subscale.Basal = lapD3Subscale.Basal;
            subscale.Ceiling = lapD3Subscale.Ceiling;
            subscale.ChronologicalAge = lapD3Subscale.ChronologicalAge;
            subscale.DateOfAssessment = lapD3Subscale.DateAssessed;
            subscale.Domain = lapD3Subscale.LapDomain;
            subscale.RawScore = lapD3Subscale.RawScore;
            subscale.SubscaleStaticID = lapD3Subscale.SubscaleStaticId;
            subscale.Instrument = AssessmentLevel.LAP_D3;

            return subscale;
        }

        private async Task<Subscale> GetCurrentSubscale(long? organizationId, long? assessmentId, long? subscaleId, int? instrument, Milestone currentStaticSubscale, Checkpoint? checkpoint, long? studentId)
        {
            Subscale? subscale = null;

            switch (instrument)
            {
                case AssessmentLevel.E_LAP:
                    subscale = await GetELapSubscale(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_3:
                    subscale = await GetLap3Subscale(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_BK:
                    subscale = await GetLapBKSubscale(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_D3:
                    subscale = await GetLapD3Subscale(organizationId, assessmentId, subscaleId);
                    break;
            }

            if (subscale != null)
            {
                subscale.DomainSequence = currentStaticSubscale.DomainSequence;
                subscale.Sequence = currentStaticSubscale.Sequence;
                subscale.SubscaleID = currentStaticSubscale.SubscaleID;
                subscale.SubscaleName = currentStaticSubscale.Subscale;
                subscale.CheckpointValue = checkpoint;
                subscale.ChildInstID = studentId;
            }
            else
            {
                throw new Exception("No Subscale Found with ID " + subscaleId);
            }

            return subscale;
        }

        private async Task<List<AssessmentItem>> GetAssessmentItems(long? organizationId, long? assessmentId, long? subscaleId, int? instrument)
        {
            List<AssessmentItem> itemList = new List<AssessmentItem>();

            switch (instrument)
            {
                case AssessmentLevel.E_LAP:
                    itemList = await _elapItemRepository.GetAssessmentItemsAsync(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_3:
                    itemList = await _lap3ItemRepository.GetAssessmentItemsAsync(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_BK:
                    itemList = await _lapBKItemRepository.GetAssessmentItemsAsync(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_D3:
                    itemList = await _lapD3ItemRepository.GetAssessmentItemsAsync(organizationId, assessmentId, subscaleId);
                    break;
                case AssessmentLevel.LAP_D_SCREEN:
                case AssessmentLevel.LAP_Screen_3YO:
                case AssessmentLevel.LAP_Screen_4YO:
                case AssessmentLevel.LAP_Screen_5YO:
                    itemList = await _lapScreenItemRepository.GetAssessmentItemsAsync(organizationId, assessmentId);
                    break;
            }

            return itemList;
        }

        private async Task<SmartLapAssessment?> GetExistingAssessmentData(long? organizationId, long? studentId, long? assessmentId, long? subscaleId, int? instrument, int? lapCheckptInt, Milestone currentStaticSubscale)
        {
            LapAssessment? assessment = await _assessmentRepository.GetLapAssessmentAsync(organizationId, studentId, assessmentId);

            // If null found then error
            if (assessment == null)
            {
                throw new Exception("No Assessment Found with ID " + assessmentId);
            }

            if (assessment.Instrument != instrument)
            {
                throw new Exception("Assessment found with incorrect instrument: Assessment inst id " + assessmentId + " Instrument " + instrument);
            }

            // Turn into smart assessment
            SmartLapAssessment? smartAssessment = SmartLapAssessment.Create(instrument);

            if (smartAssessment != null)
            {
                smartAssessment.Id = assessmentId;
                smartAssessment.DateOfAssessment = assessment.DateOfAssessment;
                smartAssessment.ChronologicalAge = assessment.ChronologicalAge;

                if (smartAssessment is CriterionAssessment criterionAssessment)
                {
                    Checkpoint? checkpoint = CheckpointExtensions.GetCheckpoint(lapCheckptInt, instrument);
                    criterionAssessment.AssessmentCheckpoint = checkpoint;

                    List<Subscale> subscaleList = new List<Subscale>();

                    // Subscales
                    if (subscaleId != null)
                    {
                        Subscale subscale = await GetCurrentSubscale(organizationId, assessmentId, subscaleId, instrument, currentStaticSubscale, checkpoint, studentId);
                        subscaleList.Add(subscale);
                    }

                    criterionAssessment.Subscales = subscaleList;
                }

                List<AssessmentItem> assessmentItemList = await GetAssessmentItems(organizationId, assessmentId, subscaleId, instrument);
                smartAssessment.SetAssessmentItems(assessmentItemList, true);
            }

            return smartAssessment;
        }

        private async Task<SmartLapAssessment?> GetLapAssessment(long? organizationId, long? studentId, long? assessmentId, long? subscaleId, int? instrument, int? lapCheckptInt, Milestone currentStaticSubscale)
        {
            SmartLapAssessment? smartAssessment;
            if (assessmentId == null)
            {
                smartAssessment = CreateSmartAssessment(instrument, lapCheckptInt);
            }
            else
            {
                smartAssessment = await GetExistingAssessmentData(organizationId, studentId, assessmentId, subscaleId, instrument, lapCheckptInt, currentStaticSubscale);
            }

            return smartAssessment;
        }

        public async Task<LapAssessmentEntryDto> GetLapAssessmentForEntry(long? organizationId, long? studentId, long? assessmentId, long? subscaleId, long? StaticSubscaleId, int? instrument, int? language, int? lapCheckptInt)
        {
            List<Milestone> staticSubscales = await GetStaticSubscales(instrument, language);

            // Get the static subscale that is for the current assessment
            Milestone currentStaticSubscale = GetCurrentStaticSubscale(StaticSubscaleId, staticSubscales);

            // Get static items
            List<Milestone> staticItems = await GetStaticItemList(instrument, language, currentStaticSubscale.SubscaleStaticID, currentStaticSubscale.SubscaleSequence);

            SmartLapAssessment? smartAssessment = await GetLapAssessment(organizationId, studentId, assessmentId, subscaleId, instrument, lapCheckptInt, currentStaticSubscale);

            if (smartAssessment == null)
            {
                throw new Exception("Failed to fetch Assessment for student " + studentId);
            }

            LapAssessmentEntryDto dto = new LapAssessmentEntryDto();
            dto.LapStaticSubscales = staticSubscales;
            dto.CurrentSubscaleInstID = StaticSubscaleId;
            dto.LapStaticItems = staticItems;
            dto.SmartAssessment = smartAssessment;

            return dto;
        }
    }
}
