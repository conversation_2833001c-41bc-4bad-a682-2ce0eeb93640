﻿@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Pages.Prompts.Generic
@using Compass.Common.Services
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject IStudentService StudentService
@inject ISiteService SitService

<EditForm Model="Input" FormName="formStudent" OnValidSubmit="CreateStudentAsync" class="c4l-form studentgroup-add-form">
    <h3 class="c4l-form-heading">@($"Add Student to: {currentStudentGroupName}")</h3>

    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>

    <FieldComponent Label="First Name" LabelFor="student-firstname">
        <Control>
            <InputText Id="student-firstname" @bind-Value="Input.FirstName" Required="true" class="form-control"></InputText>
        </Control>
    </FieldComponent>

    <FieldComponent Label="Middle Name" LabelFor="student-middlename">
        <Control>
            <InputText Id="student-middlename" @bind-Value="Input.MiddleName" class="form-control"></InputText>
        </Control>
    </FieldComponent>

    <FieldComponent Label="Last Name" LabelFor="student-lastname">
        <Control>
            <InputText Id="student-lastname" @bind-Value="Input.LastName" Required="true" class="form-control"></InputText>
        </Control>
    </FieldComponent>

    <FieldComponent Label="School Id" LabelFor="student-schoolid">
        <Control>
            <InputText Id="student-schoolid" @bind-Value="Input.SchoolId" class="form-control"></InputText>
        </Control>
        <ValidationControl>
            @if (showDuplicateSchoolIdError)
            {
                <div class="text-danger">@errorMessage</div>
            }
        </ValidationControl>
    </FieldComponent>

    <FieldComponent Label="Gender">
        <Control>
            <InputRadioGroup @bind-Value="Input.Gender">
                <div class="form-check">
                    <InputRadio Value="@("M")" class="form-check-input" id="male-radio" />
                    <label class="form-check-label" for="male-radio">Male</label>
                </div>
                <div class="form-check">
                    <InputRadio Value="@("F")" class="form-check-input" id="female-radio" />
                    <label class="form-check-label" for="female-radio">Female</label>
                </div>
            </InputRadioGroup>
        </Control>
    </FieldComponent>

    <FieldComponent Label="Birth Date" LabelFor="student-birthday">
        <Control>
            <InputDate Id="student-birthday" @bind-Value="Input.BirthDate" class="form-control" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Enroll Date" LabelFor="student-enroll-date">
        <Control>
            <InputDate Id="student-enroll-date" @bind-Value="Input.EnrollDate" class="form-control" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Hispanic Latino" LabelFor="student-hispanic-latino">
        <Control>
            <InputCheckbox Id="student-hispanic-latino" class="form-check-input" @bind-Value="Input.HispanicLatino" />
        </Control>
    </FieldComponent>

    @foreach(StudentRace race in studentRaceList)
    {
        var formattedRaceId = SanitizeRaceId(race.Race);

        <FieldComponent Label="@race.Race" LabelFor="@formattedRaceId">
            <Control>
                <input 
                    type="checkbox" 
                    id="@formattedRaceId"
                    class="form-check-input"
                    value="@race.Id" 
                    checked="@(Input.SelectedRaces != null && Input.SelectedRaces.Contains(race.Id))" 
                    @onchange="(e) => ToggleRaceSelections(race.Id, e.Value as bool?)" 
                />
            </Control>
        </FieldComponent>
    }

    <FieldComponent Label="Language" LabelFor="student-language">
        <Control>
            <InputSelect id="student-language" @bind-Value="Input.Language">
                <option value="" disabled>-- Language --</option>
                @foreach (StudentLanguage language in studentLanguageList)
                {
                    <option value="@language.Id">@language.Language</option>
                }
            </InputSelect>
        </Control>
        <ValidationControl>
            <ValidationMessage For="@(() => Input.Language)" />
        </ValidationControl>
    </FieldComponent>

    <FieldComponent Label="Free/Reduced Lunch" LabelFor="student-free-reduced-lunch">
        <Control>
            <InputCheckbox Id="student-free-reduced-lunch" class="form-check-input" @bind-Value="Input.SchoolLunch" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Dual Language Learner" LabelFor="student-dual-language-learner">
        <Control>
            <InputCheckbox Id="student-dual-language-learner" class="form-check-input" @bind-Value="Input.DualLanguage" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="IFSP/IEP" LabelFor="student-ifsp-iep">
        <Control>
            <InputCheckbox Id="student-ifsp-iep" class="form-check-input" @bind-Value="Input.IEPIFSP" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Has Disability?" LabelFor="student-has-disability">
        <Control>
            <InputCheckbox Id="student-has-disability" class="form-check-input" @bind-Value="Input.HasDisability" />
        </Control>
    </FieldComponent>

    @if (Input.HasDisability)
    {
        <FieldComponent Label="Disability" LabelFor="student-disability">
            <Control>
                <InputText Id="student-disability" @bind-Value="Input.Disability" Required="true" class="form-control"></InputText>
            </Control>
        </FieldComponent>
    }

    <div class="form-submit-buttons-wrapper">
        <button class="c4l-button c4l-form-button c4l-secondary-button" type="submit">Add Student</button>

        @if (showSuccessMessage)
        {
            <div class="alert alert-success fade show" role="alert">
                @successMessage
            </div>
        }
    </div>
</EditForm>

<MessageBox 
    Title="Attention"
    Message="Site does not have a current school year set up. Please have your site admin set your current school year"
    IsVisible="@showError"
    IsLocalized=true
    OnClose="HideErrorMessage" 
/>
