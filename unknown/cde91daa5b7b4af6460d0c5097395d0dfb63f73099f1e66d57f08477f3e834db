﻿using Microsoft.AspNetCore.Components;

namespace Compass.LAP.Pages.Summary
{
    public partial class LAP_StudentSummary
    {
        [Inject]
        public required NavigationManager NavigationManager { get; set; }

        private void OnAssessmentSummaryClick()
        {
            NavigationManager.NavigateTo($"/lap-student-assessment-summary");
        }

        private void OnAssessmentEntryClick()
        {
            NavigationManager.NavigateTo($"/lap-guided-entry");
        }
    }
}
