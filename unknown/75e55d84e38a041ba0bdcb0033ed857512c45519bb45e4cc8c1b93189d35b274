﻿using Compass.Common.Data;
using Compass.LAP.DTOs;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class AssessmentRepository : IAssessmentRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public AssessmentRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public Task<bool> DeleteAssessmentsAsync(List<string> assessmentIds)
        {
            throw new NotImplementedException();
        }

        public Task<(List<LapChildRowContainer> Rows, SubscaleContainer Subscales, int CurrentCheckpoint)> GetAssessmentSummaryAsync(long classroomId, int checkpoint, int language, HashSet<int> registeredInstruments, List<int> hiddenInstruments)
        {
            throw new NotImplementedException();
        }

        public Task<List<LapChildRowContainer>> GetChildAssessmentsSummaryAsync(long childId, HashSet<int> registeredInstruments, SubscaleContainer subscales, long classroomId)
        {
            throw new NotImplementedException();
        }

        public Task<SubscaleContainer> GetSubscaleContainerAsync(HashSet<int> registeredInstruments)
        {
            throw new NotImplementedException();
        }

        public async Task<LapAssessment?> GetLapAssessmentAsync(long? organizationId, long? studentId, long? assessmentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentId is null)
            {
                throw new ArgumentNullException(nameof(studentId));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                LapAssessment? assessment = await _dbContext.LapAssessments
                    .FirstOrDefaultAsync(a => a.OrganizationId == organizationId.Value
                             && a.StudentId == studentId.Value
                             && a.Id == assessmentId.Value);

                return assessment;
            }
        }
    }
}
