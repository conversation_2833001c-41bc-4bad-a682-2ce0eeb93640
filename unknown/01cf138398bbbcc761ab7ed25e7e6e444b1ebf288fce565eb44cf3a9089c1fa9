using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Components
{
    public partial class C4L_LessonDetailsOverviewComponent
    {
        [Inject]
        private IC4LSessionStateService c4LSessionStateService { get; set; } = default!;

        [Parameter] public C4LLessonContentDto LessonContentDto { get; set; } = new C4LLessonContentDto();
        [Parameter] public bool IsPreparationCompleted { get; set; } = false;
        [Parameter] public string Language { get; set; } = "English";
        [Parameter] public int Unit { get; set; }
        [Parameter] public int Week { get; set; }
        [Parameter] public int Day { get; set; }
        [Parameter] public int LessonTypeSequence { get; set; }
        [Parameter] public int TitleSequence { get; set; }
        [Parameter] public long? ClassroomId { get; set; }
        [Parameter] public long? OrganizationId { get; set; }
        [Parameter] public string? CurrentUserId { get; set; }
        [Parameter] public EventCallback<bool> OnPreparationStatusChanged { get; set; }

        [Inject] private UserAccessor UserAccessor { get; set; } = default!;
        [Inject] private UserSessionService UserSessionService { get; set; } = default!;
        [Inject] private IC4LClassroomService C4LClassroomService { get; set; } = default!;
        [Inject] private IC4LLessonPreparationCompletedService LessonPreparationCompletedService { get; set; } = default!;
        [Inject] private IC4LLessonPreparationService LessonPreparationService { get; set; } = default!;
        [Inject] private ILogger<C4L_LessonDetailsOverviewComponent> Logger { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            await CheckPreparationStatus();
        }

        private async Task<CommonSessionData?> GetSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();

            if (userId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(userId);
                if ( commonSessionData is not null)
                {
                    CurrentUserId = userId;
                    OrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            C4LNavigationContext navigationContext = await c4LSessionStateService.GetNavigationContextAsync();
            C4LLessonContext lessonContext = await c4LSessionStateService.GetLessonContextAsync();

            Unit = lessonContext.SelectedLessonUnit;
            Week = lessonContext.SelectedLessonWeek;
            Day = lessonContext.SelectedLessonDay;
            LessonTypeSequence = lessonContext.SelectedLessonTypeSequence;
            TitleSequence = lessonContext.SelectedLessonTitleSequence;
            Language = lessonContext.SelectedLessonLanguage;
            ClassroomId = navigationContext.C4L_ClassroomId;

            return commonSessionData;
        }

        public async Task OnInitializeAsync()
        {
            await CheckPreparationStatus();
        }

        public async Task CheckPreparationStatus()
        {
            try
            {
                // Get the organization ID from the session
                CommonSessionData? commonSessionData = await GetSessionData();

                if (ClassroomId.HasValue && UserSessionService != null)
                {
                    if (OrganizationId.HasValue)
                    {
                        C4LLessonPreparationCompleted preparationCompleted = new C4LLessonPreparationCompleted
                        {
                            OrganizationId = OrganizationId.Value,
                            C4LClassroomId = ClassroomId.Value,
                            Unit = Unit,
                            Week = Week,
                            Day = Day,
                            LessonTypeSequence = LessonTypeSequence,
                            TitleSequence = TitleSequence
                        };

                        C4LLessonPreparationCompleted? completedPrep = await LessonPreparationCompletedService.GetAsync(preparationCompleted);

                        bool newStatus = completedPrep != null && completedPrep.DateCompleted.HasValue;
                        if (newStatus != IsPreparationCompleted)
                        {
                            await OnPreparationStatusChanged.InvokeAsync(newStatus);
                        }

                        IsPreparationCompleted = newStatus;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error with Serilog
                Logger.LogError(ex, "Error checking preparation status: {ErrorMessage}", ex.Message);

                // Also display in console for immediate visibility during development
                Console.WriteLine($"Error checking preparation status: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private async Task UpdateCompletedStatus(bool isComplete)
        {
            try
            {
                if (CurrentUserId != null && ClassroomId.HasValue && UserSessionService != null)
                {
                    // Get the organization ID from the session
                    CommonSessionData? commonSessionData = await GetSessionData();

                    if (commonSessionData is not null)
                    {
                        long? orgId = OrganizationId ?? commonSessionData.CurrentOrganizationId;
                        if (orgId.HasValue)
                        {
                            C4LLessonPreparationCompleted preparationCompleted = new C4LLessonPreparationCompleted
                            {
                                OrganizationId = orgId.Value,
                                C4LClassroomId = ClassroomId.Value,
                                Unit = Unit,
                                Week = Week,
                                Day = Day,
                                LessonTypeSequence = LessonTypeSequence,
                                TitleSequence = TitleSequence
                            };

                            // Save the completion status
                            await LessonPreparationCompletedService.SaveCompletionStatusAsync(preparationCompleted, isComplete);

                            // Notify parent component of the status change
                            await OnPreparationStatusChanged.InvokeAsync(isComplete);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error with Serilog
                Logger.LogError(ex, "Error updating preparation completion status to {IsComplete}: {ErrorMessage}",
                    isComplete, ex.Message);

                // Also display in console for immediate visibility during development
                Console.WriteLine($"Error updating preparation completion status: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
