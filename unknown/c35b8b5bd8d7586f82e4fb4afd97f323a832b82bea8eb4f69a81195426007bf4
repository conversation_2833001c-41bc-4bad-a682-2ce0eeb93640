using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class ELAPSubScaleRepository : IELAPSubScaleRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public ELAPSubScaleRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<ELAPSubScale> SaveAsync(ELAPSubScale elapSubScale)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (elapSubScale.Id == 0)
                {
                    // Create new record
                    _dbContext.ELAPSubScales.Add(elapSubScale);
                }
                else
                {
                    // Update existing record
                    _dbContext.ELAPSubScales.Update(elapSubScale);
                }

                await _dbContext.SaveChangesAsync();
                return elapSubScale;
            }
        }

        public async Task<ELAPSubScale?> GetByIdAsync(long? organizationId, long? id, long? assessmentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.ELAPSubScales
                    .FirstOrDefaultAsync(e => e.Id == id.Value && e.OrganizationId == organizationId.Value && e.AssessmentId == assessmentId.Value);
            }
        }
    }
}
