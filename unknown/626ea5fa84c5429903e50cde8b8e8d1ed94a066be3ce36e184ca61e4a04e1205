using Compass.LAP.DTOs;
using Compass.LAP.Models;

namespace Compass.LAP.Interfaces.Repositories
{
    public interface IAssessmentRepository
    {
        public Task<(List<LapChildRowContainer> Rows, SubscaleContainer Subscales, int CurrentCheckpoint)> GetAssessmentSummaryAsync(
            long classroomId,
            int checkpoint,
            int language,
            HashSet<int> registeredInstruments,
            List<int> hiddenInstruments);

        public Task<List<LapChildRowContainer>> GetChildAssessmentsSummaryAsync(
            long childId,
            HashSet<int> registeredInstruments,
            SubscaleContainer subscales,
            long classroomId);

        public Task<bool> DeleteAssessmentsAsync(List<string> assessmentIds);

        public Task<SubscaleContainer> GetSubscaleContainerAsync(HashSet<int> registeredInstruments);

        public Task<LapAssessment?> GetLapAssessmentAsync(long? organizationId, long? studentId, long? assessmentId);
    }
}
