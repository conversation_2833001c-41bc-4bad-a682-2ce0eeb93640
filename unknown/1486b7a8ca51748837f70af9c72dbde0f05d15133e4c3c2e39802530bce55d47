using Compass.C4L.Models;
using Compass.Common.DTOs.Entity1;
using Compass.Common.DTOs.Entity2;
using Compass.Common.DTOs.Entity3;
using Compass.Common.DTOs.LicensePool;
using Compass.Common.DTOs.Organization;
using Compass.Common.DTOs.Site;
using Compass.Common.DTOs.Student;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.DTOs.User;
using Compass.Common.Models;
using Compass.Deca.Models;
using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Compass.Common.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
    {
        public DbSet<ApplicationUser> ApplicationUsers { get; set; }
        public DbSet<Organization> Organizations { get; set; }
        public DbSet<LicensePool> LicensePools { get; set; }
        public DbSet<OrganizationHierarchy> OrganizationHierarchies { get; set; }
        public DbSet<OrganizationListDisplayDto> OrganizationListDisplayDtos { get; set; }
        public DbSet<LicensePoolListDisplayDto> LicensePoolListDisplayDtos { get; set; }
        public DbSet<Entity1> Entities1 { get; set; }
        public DbSet<Entity2> Entities2 { get; set; }
        public DbSet<Entity3> Entities3 { get; set; }
        public DbSet<Site> Sites { get; set; }
        public DbSet<SchoolYear> SchoolYears { get; set; }
        public DbSet<StudentGroup> StudentGroups { get; set; }
        public DbSet<StudentGroupRoster> StudentGroupRosters { get; set; }
        public DbSet<Student> Students { get; set; }
        public DbSet<StudentLanguage> StudentLanguages { get; set; }
        public DbSet<StudentRace> StudentRaces { get; set; }
        public DbSet<StudentRaceLink> StudentRaceLinks { get; set; }
        public DbSet<UserOrganizationAccess> UserOrganizationAccesses { get; set; }
        public DbSet<UserOrganizationLink> UserOrganizationLinks { get; set; }
        public DbSet<VisibleEntity> VisibleEntities { get; set; }
        public DbSet<Entity1ListDisplayDto> Entity1ListDisplayDtos { get; set; }
        public DbSet<Entity2ListDisplayDto> Entity2ListDisplayDtos { get; set; }
        public DbSet<Entity3ListDisplayDto> Entity3ListDisplayDtos { get; set; }
        public DbSet<SiteListDisplayDto> SiteListDisplayDtos { get; set; }
        public DbSet<StudentGroupListDisplayDto> StudentGroupListDisplayDtos { get; set; }
        public DbSet<StudentDisplayDto> StudentDisplayDtos { get; set; }
        public DbSet<UserEntity1Access> UserEntity1Accesses { get; set; }
        public DbSet<UserEntity1Link> UserEntity1Links { get; set; }
        public DbSet<UserEntity2Access> UserEntity2Accesses { get; set; }
        public DbSet<UserEntity2Link> UserEntity2Links { get; set; }
        public DbSet<UserEntity3Access> UserEntity3Accesses { get; set; }
        public DbSet<UserEntity3Link> UserEntity3Links { get; set; }
        public DbSet<UserSiteAccess> UserSiteAccesses { get; set; }
        public DbSet<UserSiteLink> UserSiteLinks { get; set; }
        public DbSet<UserStudentGroupAccess> UserStudentGroupAccesses { get; set; }
        public DbSet<UserStudentGroupLink> UserStudentGroupLinks { get; set; }
        public DbSet<UserListDisplayDto> UserListDisplayDtos { get; set; }
        public DbSet<UserAssignmentDto> UserAssignmentDtos { get; set; }

        // C4L
        public DbSet<C4LLesson> C4LLessons { get; set; }
        public DbSet<C4LLearningCenter> C4LLearningCenters { get; set; }
        public DbSet<C4LLearningObjective> C4LLearningObjectives { get; set; }
        public DbSet<C4LNonContactDay> C4LNonContactDays { get; set; }
        public DbSet<C4LClassroom> C4LClassrooms { get; set; }
        public DbSet<C4LLicenseStudentGroup> C4LLicenseStudentGroups { get; set; }
        public DbSet<C4LLessonPreparation> C4LLessonPreparations { get; set; }
        public DbSet<C4LLessonPreparationCompleted> C4LLessonPreparationCompleteds { get; set; }
        public DbSet<C4LGameToken> C4LGameTokens { get; set; }

        // DECA
        public DbSet<DecaStudentRating> DecaStudentRatings { get; set; }
        public DbSet<DecaStudentRatingScore> DecaStudentRatingScores { get; set; }
        public DbSet<DecaQuestion> DecaQuestions { get; set; }
        public DbSet<DecaStudentContact> DecaStudentContacts { get; set; }

        //LAP
        public DbSet<LapAssessment> LapAssessments { get; set; }
        public DbSet<ELAPSubScale> ELAPSubScales { get; set; }
        public DbSet<LAP3SubScale> LAP3SubScales { get; set; }
        public DbSet<LAPBKSubScale> LAPBKSubScales { get; set; }
        public DbSet<LAPD3SubScale> LAPD3SubScales { get; set; }
        public DbSet<ELAPItem> ELAPItems { get; set; }
        public DbSet<LAP3Item> LAP3Items { get; set; }
        public DbSet<LAPBKItem> LAPBKItems { get; set; }
        public DbSet<LAPD3Item> LAPD3Items { get; set; }
        public DbSet<LAPScreenItem> LAPScreenItems { get; set; }
        public DbSet<AssessmentItem> AssessmentItems { get; set; }
        public DbSet<LAPSubScalesLookup> LAPSubScalesLookups { get; set; }
        public DbSet<LAPItemsLookup> LAPItemsLookups { get; set; }


        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<VisibleEntity>().HasNoKey();
        }
    }
}
