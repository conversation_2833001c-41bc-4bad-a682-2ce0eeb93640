using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Interfaces.Repositories
{
    public interface ILAPBKItemRepository
    {
        Task<LAPBKItem> SaveAsync(LAPBKItem lapbkItem);
        Task<List<LAPBKItem>> GetItemsBySubscaleAsync(long? organizationId, long? assessmentId, long? subscaleId);
        Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId, long? subscaleId);
    }
}
