using Compass.LAP.Resources.Instruments;

namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public class Milestone
    {
        public static string ITEM_SEPARATOR = "|";
        public const int U_LAP_EF_SEQUENCE = 6;

        public long Id { get; set; }
        public string ItemID { get; set; } = string.Empty;
        public string Domain { get; set; } = string.Empty;
        public string Subscale { get; set; } = string.Empty;
        public string SubscaleID { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int? Months { get; set; }
        public string Skill { get; set; } = string.Empty;
        public string Materials { get; set; } = string.Empty;
        public int? Instrument { get; set; }
        public int? Sequence { get; set; }
        public string Comment { get; set; } = string.Empty;
        public int? Language { get; set; }
        public string AutoScoreItemNo { get; set; } = string.Empty;
        public string AutoScoreItemNoFwd { get; set; } = string.Empty;
        public int? MinSelectionRequired { get; set; }
        public string CollectionMethods { get; set; } = string.Empty;
        public int? DomainSequence { get; set; }
        public int? SubscaleSequence { get; set; }
        public int? ItemNo { get; set; }
        public bool DualLanguage { get; set; }
        public string Procedure { get; set; } = string.Empty;
        public long? SubscaleStaticID { get; set; }
        public long? ItemStaticID { get; set; }

        public void SetDomain(string domain)
        {
            Domain = domain;
            if (!string.IsNullOrEmpty(domain) && domain.Contains("Dual Language"))
            {
                DualLanguage = true;
            }
        }

        public bool IsSingleSelection()
        {
            return Instrument == AssessmentLevel.LAP_BK &&
                   SubscaleSequence == U_LAP_EF_SEQUENCE;
        }
    }
}
