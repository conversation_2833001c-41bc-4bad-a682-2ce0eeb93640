using Compass.LAP.Resources.SmartAssessment.Criterion;
using Compass.LAP.Resources.SmartAssessment.Observation;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Resources.SmartAssessment.Ongoing
{
    public abstract class OngoingAssessment : CriterionAssessment
    {
        public override void UpdateSubscale(DateTime? selectedDate, AssessmentItem item, int? lastItemSequence)
        {
            base.UpdateSubscale(selectedDate, item, lastItemSequence);

            // if it's an ongoing checkpoint, always update the subscale date since it
            // can be edited after it's original assessment date. that way, we ensure that new
            // assessments will always get the most recent items if the user has been updating the
            // ongoing checkpoint.
            Subscale? s = GetSubscale(item.SubscaleID);
            if (AssessmentCheckpoint == Checkpoint.ONGOING)
            {
                if (s != null)
                {
                    s.DateOfAssessment = selectedDate;
                }
            }
        }

        public override void SetAssessmentItems(List<AssessmentItem>? assessmentItems, bool replace)
        {
            if (AssessmentCheckpoint == Checkpoint.ONGOING && !replace && AssessmentItems.Count > 0 && assessmentItems != null)
            {
                AddItemsForOngoing(assessmentItems);
            }
            else
            {
                base.SetAssessmentItems(assessmentItems, replace);
            }
        }

        private void AddItemsForOngoing(List<AssessmentItem> assessmentItems)
        {
            List<AssessmentItem> nItems = assessmentItems;

            // don't use passed item if current item assess date is more recent.
            // Note: We need to iterate backwards to avoid collection modification issues
            for (int i = AssessmentItems.Count - 1; i >= 0; i--)
            {
                AssessmentItem cItem = AssessmentItems[i];
                if (nItems.Contains(cItem))
                {
                    int nItemIndex = nItems.IndexOf(cItem);
                    AssessmentItem nItem = nItems[nItemIndex];

                    if (nItem.AssessDate != null && cItem.AssessDate != null &&
                        nItem.AssessDate.Value > cItem.AssessDate.Value)
                    {
                        AssessmentItems.Remove(cItem);
                        AssessmentItems.Add(nItem);
                    }
                }
            }
        }

        public override int GetNumberOfItemsForBasal()
        {
            return 8;
        }

        public override int? GetNoBasalItemRetreat()
        {
            return GetNumberOfItemsForBasal() * -1;
        }

        public static SmartLapAssessment? GetMostCurrentLapAssessment(List<SmartLapAssessment> assessmentList)
        {
            SmartLapAssessment? ret = null;
            Subscale? latestSubscale = null;

            foreach (SmartLapAssessment assessment in assessmentList)
            {
                ICollection<Subscale> subscaleCol = ((CriterionAssessment)assessment).Subscales;

                if (subscaleCol != null && subscaleCol.Count > 0)
                {
                    Subscale currentSubscale = subscaleCol.First();

                    if (currentSubscale.Ceiling != null)
                    {
                        if (latestSubscale == null)
                        {
                            latestSubscale = currentSubscale;
                        }

                        DateTime? latestDate = latestSubscale.DateOfAssessment;
                        DateTime? assessDate = currentSubscale.DateOfAssessment;

                        if (latestDate != null && assessDate != null && latestDate < assessDate)
                        {
                            latestSubscale = currentSubscale;
                        }
                    }
                }
            }

            if (latestSubscale != null)
            {
                foreach (SmartLapAssessment assessment in assessmentList)
                {
                    if (assessment.Id == latestSubscale.Id)
                    {
                        ret = assessment;
                        break;
                    }
                }
            }

            return ret;
        }

        public static Subscale? GetLatestSubscaleFromAssessments(List<SmartLapAssessment> assessmentList)
        {
            Subscale? latestSubscale = null;

            foreach (SmartLapAssessment assessment in assessmentList)
            {
                ICollection<Subscale> subscaleCol = ((CriterionAssessment)assessment).Subscales;

                if (subscaleCol != null && subscaleCol.Count > 0)
                {
                    Subscale currentSubscale = subscaleCol.First();

                    if (currentSubscale.Ceiling != null)
                    {
                        if (latestSubscale == null)
                        {
                            latestSubscale = currentSubscale;
                        }

                        DateTime? latestDate = latestSubscale.DateOfAssessment;
                        DateTime? assessDate = currentSubscale.DateOfAssessment;

                        if (latestDate != null && assessDate != null && latestDate < assessDate)
                        {
                            latestSubscale = currentSubscale;
                        }
                    }
                }
            }

            return latestSubscale;
        }

        public static OngoingAssessment GetOngoingItems(SmartLapAssessment? prevAssessment, SmartLapAssessment assessment)
        {
            if (prevAssessment != null &&
                ((CriterionAssessment)prevAssessment).Subscales.Count > 0)
            {
                assessment.ChronologicalAge = prevAssessment.ChronologicalAge;

                foreach (AssessmentItem item in prevAssessment.AssessmentItems)
                {
                    item.Id = null;
                    item.AssessmentInstID = null;
                    item.ChronologicalAge = assessment.ChronologicalAge;
                    item.Checkpoint = ((CriterionAssessment)assessment).AssessmentCheckpoint;
                    item.SubscaleInstID = null;
                }

                assessment.SetAssessmentItems(prevAssessment.AssessmentItems, false);
                ((CriterionAssessment)assessment).Subscales = ((CriterionAssessment)prevAssessment).Subscales;

                Subscale s = ((CriterionAssessment)assessment).Subscales.First();
                s.AssessmentInstID = null;
                s.RawScore = null;
                s.Ceiling = null;
                s.ChronologicalAge = assessment.ChronologicalAge;
                s.CheckpointValue = ((CriterionAssessment)assessment).AssessmentCheckpoint;
            }

            return (OngoingAssessment)assessment;
        }

        public OngoingAssessment GetObservationItems(SmartLapAssessment assessment, List<ProgressObservation> observations, long? customerInstID, ICollection<Milestone> milestones)
        {
            foreach (ProgressObservation observation in observations)
            {
                if (observation.Items != null)
                {
                    foreach (ProgressObservationItem obsItem in observation.Items)
                    {
                        if (obsItem.StaticItem != null)
                        {
                            Milestone obsStaticItm = obsItem.StaticItem;

                            if (obsItem.StaticItem != null && obsItem.AssessmentItemInstID == null)
                            {
                                Subscale? subscale = null;
                                foreach (Subscale sub in ((OngoingAssessment)assessment).Subscales)
                                {
                                    if (sub.SubscaleStaticID != null && sub.SubscaleStaticID.Equals(obsItem.SubscaleStaticID))
                                    {
                                        subscale = sub;
                                        break;
                                    }
                                }

                                int? basal = null;

                                if (subscale != null)
                                {
                                    basal = subscale.Basal;
                                }

                                if (basal == null || obsStaticItm.Sequence > basal)
                                {
                                    AssessmentItem item = new AssessmentItem();
                                    item.AssessmentInstID = assessment.Id;
                                    item.AssessDate = observation.DateOfObservation;
                                    item.AchieveDate = observation.DateOfObservation;
                                    item.ChronologicalAge = assessment.ChronologicalAge;
                                    item.CustomerInstID = customerInstID;
                                    item.Instrument = assessment.Instrument;

                                    if (observation.CrossDomainScoreStaticId != null)
                                    {
                                        item.Value = obsItem.CrossScore;
                                    }
                                    else
                                    {
                                        item.Value = Skill.MASTERED;
                                    }

                                    item.ObserverInstID = observation.ObserverInstID;
                                    if (subscale != null)
                                    {
                                        item.SubscaleInstID = subscale.Id;
                                    }
                                    item.SubscaleStaticID = obsItem.SubscaleStaticID;
                                    item.ItemStaticID = obsItem.ItemStaticID;
                                    item.Sequence = obsStaticItm.Sequence;
                                    item.ProgressObservationItem = obsItem;
                                    DateTime? obsDate = observation.DateOfObservation;
                                    item.ObservationDate = obsDate;

                                    item.UserComment = "This item was scored via observation " + obsDate?.ToString();

                                    bool replacedItem = false;
                                    for (int i = 0; i < assessment.AssessmentItems.Count; i++)
                                    {
                                        AssessmentItem currentItem = assessment.AssessmentItems[i];
                                        if (currentItem.Sequence != null && currentItem.Sequence.Equals(item.Sequence))
                                        {
                                            assessment.AssessmentItems[i] = item;
                                            replacedItem = true;
                                            break;
                                        }
                                    }

                                    if (!replacedItem)
                                    {
                                        assessment.AssessmentItems.Add(item);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return (OngoingAssessment)assessment;
        }
    }
}
