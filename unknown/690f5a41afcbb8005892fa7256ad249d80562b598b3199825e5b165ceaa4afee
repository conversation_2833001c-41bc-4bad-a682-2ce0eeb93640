﻿using Compass.Common.Data;
using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.Models;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Compass.Common.Pages.Admin.Student
{
    public partial class StudentAddComponent
    {
        [Parameter]
        public EventCallback OnReturn { get; set; }

        private long? organizationId;
        private long? siteId;
        private long? studentGroupId;

        private List<StudentRace> studentRaceList = new();
        private List<StudentLanguage> studentLanguageList = new();

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private bool showSuccessMessage = false;
        private bool showError = false;
        private string successMessage = "Information saved successfully!";
        private string currentStudentGroupName = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private bool showDuplicateSchoolIdError = false;
        private string errorMessage = string.Empty;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            showSuccessMessage = false;
            showError = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.organizationId = commonSessionData.CurrentOrganizationId;
                this.siteId = commonSessionData.CurrentSiteId;
                this.studentGroupId = commonSessionData.CurrentStudentGroupId;
                currentStudentGroupName = commonSessionData.SelectedEntityName;

                StudentFormFieldsDto formFields = await StudentService.GetStudentFormFields();
                studentRaceList = formFields.StudentRaceList;
                studentLanguageList = formFields.StudentLanguageList;
            }
        }

        private string TransalteBoolToString(bool value)
        {
            string ret;
            if (value)
            {
                ret = "Y";
            }
            else
            {
                ret = "N";
            }

            return ret;
        }

        private Compass.Common.Models.Student CreateStudentValues()
        {
            Compass.Common.Models.Student student = new();
            student.OrganizationId = organizationId;
            student.FirstName = Input.FirstName;
            student.MiddleName = Input.MiddleName;
            student.LastName = Input.LastName;
            student.SchoolId = Input.SchoolId;

            student.Gender = Input.Gender;

            student.BirthDate = Input.BirthDate;
            student.EnrollDate = Input.EnrollDate;

            string hispanicLatino = TransalteBoolToString(Input.HispanicLatino);
            student.HispanicLatino = hispanicLatino;

            student.PrimaryLanguageId = Input.Language == 0 ? 1 : Input.Language;

            string schoolLunch = TransalteBoolToString(Input.SchoolLunch);
            student.SchoolLunch = schoolLunch;

            string dll = TransalteBoolToString(Input.DualLanguage);
            student.DualLanguage = dll;

            string iepifsp = TransalteBoolToString(Input.IEPIFSP);
            student.IepIfsp = iepifsp;

            if (Input.HasDisability)
            {
                student.Disability = Input.Disability;
            }
            else
            {
                student.Disability = string.Empty;
            }

            return student;
        }

        private List<StudentRaceLink> CreateRaceLinks()
        {
            List<StudentRaceLink> linkList = new();

            List<long> raceIdList = Input.SelectedRaces;
            foreach (long raceId in raceIdList)
            {
                StudentRaceLink link = new();
                link.OrganizationId = organizationId;
                link.StudentRaceId = raceId;
                linkList.Add(link);
            }

            return linkList;
        }

        private string SanitizeRaceId(string race_string)
        {
            if(string.IsNullOrEmpty(race_string)) return string.Empty;
            
            string sanitized_race_string = Regex.Replace(race_string, @"[^a-zA-Z0-9\-]", "-");
            sanitized_race_string = Regex.Replace(sanitized_race_string, @"-+", "-");
            sanitized_race_string = sanitized_race_string.Trim('-');
            return "student-" + sanitized_race_string;
        }

        public async Task CreateStudentAsync(EditContext editContext)
        {
            try
            {
                SchoolYear? schoolYear = await SitService.GetCurrentSchoolYear(this.organizationId, this.siteId);

                if (schoolYear != null)
                {
                    Compass.Common.Models.Student student = CreateStudentValues();
                    List<StudentRaceLink> raceLinkList = CreateRaceLinks();

                    SaveStudentAction action = new();
                    action.Student = student;
                    action.RaceLinkList = raceLinkList;
                    action.StudentGroupId = studentGroupId;

                    await StudentService.SaveStudentAsync(action);
                    await OnReturn.InvokeAsync();
                }
                else
                {
                    showError = true;
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("School ID must be unique"))
            {
                showDuplicateSchoolIdError = true;
                errorMessage = $"School ID '{Input.SchoolId}' is already in use. Please use a unique School ID.";
                StateHasChanged();
            }
        }

        protected void HideErrorMessage()
        {
            showError = false;
        }

        protected void ToggleRaceSelections(long value, bool? isChecked)
        {
            if (isChecked == true)
            {
                if (!Input.SelectedRaces.Contains(value))
                    Input.SelectedRaces.Add(value);
            }
            else
            {
                Input.SelectedRaces.Remove(value);
            }
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "First Name")]
            public string FirstName { get; set; } = string.Empty;

            [Display(Name = "Middle Name")]
            public string MiddleName { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Last Name")]
            public string LastName { get; set; } = string.Empty;

            [Display(Name = "School Id")]
            public string SchoolId { get; set; } = string.Empty;

            [Display(Name = "Gender")]
            public string Gender { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Birth Date")]
            public DateTime BirthDate { get; set; }

            [Required]
            [Display(Name = "Enroll Date")]
            public DateTime EnrollDate { get; set; }

            [Display(Name = "Hispanic Latino")]
            public bool HispanicLatino { get; set; }

            [Display(Name = "Race")]
            public List<long> SelectedRaces { get; set; } = new();

            [Required]
            [Display(Name = "Language")]
            public int Language { get; set; } = 1;

            [Display(Name = "Free/Reduced Lunch")]
            public bool SchoolLunch { get; set; }

            [Display(Name = "Dual Language Learner")]
            public bool DualLanguage { get; set; }

            [Display(Name = "IFSP/IEP")]
            public bool IEPIFSP { get; set; }

            [Display(Name = "Disability")]
            public string Disability { get; set; } = string.Empty;

            public bool HasDisability { get; set; }
        }
    }
}
