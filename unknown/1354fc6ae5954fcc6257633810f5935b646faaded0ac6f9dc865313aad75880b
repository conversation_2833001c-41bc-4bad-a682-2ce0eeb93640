@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="lesson-section">
    <h2>Teacher Reflection</h2>
    <p>Teacher reflection content will be added in the future.</p>
</div>

<style>
    .lesson-section {
        margin-bottom: 20px;
    }

    h2 {
        color: var(--c4l-primary-purple);
        font-size: 22px;
        margin-bottom: 15px;
        font-weight: bold;
    }

    p {
        line-height: 1.6;
        margin-bottom: 15px;
    }
</style>
