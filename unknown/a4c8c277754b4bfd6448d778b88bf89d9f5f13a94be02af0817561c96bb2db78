﻿using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface ISchoolYearRepository
    {
        public Task<SchoolYear> CreateSchoolYearAsync(SchoolYear schoolYear);
        public Task<SchoolYear?> GetSchoolYearByIdAsync(long id);
        public Task<SchoolYear?> UpdateSchoolYearAsync(long? id, SchoolYear schoolYear);
        public Task<SchoolYear?> GetExistingSchoolYearAsync(int year, long siteId, long organizationId);
        Task<int?> GetCurrentSchoolYearForSiteAsync(long? siteId, long? organizationId);
        public Task<List<SchoolYear>> GetSchoolYearList(SchoolYearListAction action);
        public Task<int> GetSchoolYearCount(long? organizationId, long? siteId);
        public Task<SchoolYear?> GetCurrentSchoolYear(long? organizationId, long? siteId);
        public Task<int> CloseSchoolYears(long? organizationId, long? siteId);
    }
}
