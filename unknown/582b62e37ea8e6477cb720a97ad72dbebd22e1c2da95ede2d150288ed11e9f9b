﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Services;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Compass.LAP.DTOs;
using Compass.LAP.Resources.Instruments;
using Microsoft.AspNetCore.Components;

namespace Compass.LAP.Pages.Student.Assessment.Summary
{
    public partial class LAP_StudentAssessmentSummary
    {
        [Inject]
        public required UserAccessor UserAccessor { get; set; }
        [Inject]
        public required UserSessionService UserSessionService { get; set; }
        [Inject]
        public required IStudentService StudentService { get; set; }

        private long? organizationId;
        private long? studentId;
        private string? studentName;
        private DateTime? studentBirthdate;

        //TODO We could have a list of all beggining assessments or all instruments for the child?
        private List<LapAssessmentContainerDto> ElapAssessmentContainerList = new List<LapAssessmentContainerDto>();

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        private List<LapSubscaleContainerDto> CreateFakeSubscales(long? assessmentId)
        {
            List<LapSubscaleContainerDto> ret = new List<LapSubscaleContainerDto>();

            for (int i = 0; i < 3; i++)
            {
                LapSubscaleContainerDto completeContainer = new LapSubscaleContainerDto();
                completeContainer.SubscaleId = i + 1;
                completeContainer.AssessmentId = assessmentId;
                completeContainer.OrganizationId = organizationId;
                completeContainer.SubscaleSequence = i + 1;
                completeContainer.RawScore = 50;
                completeContainer.IsValid = true;
                ret.Add(completeContainer);
            }

            LapSubscaleContainerDto progressContainer = new LapSubscaleContainerDto();
            progressContainer.SubscaleId = 4;
            progressContainer.AssessmentId = assessmentId;
            progressContainer.OrganizationId = organizationId;
            progressContainer.SubscaleSequence = 4;
            progressContainer.RawScore = null;
            progressContainer.IsValid = true;
            ret.Add(progressContainer);

            LapSubscaleContainerDto invalidContainer = new LapSubscaleContainerDto();
            invalidContainer.SubscaleId = 5;
            invalidContainer.AssessmentId = assessmentId;
            invalidContainer.OrganizationId = organizationId;
            invalidContainer.SubscaleSequence = 5;
            invalidContainer.RawScore = 20;
            invalidContainer.IsValid = false;
            ret.Add(invalidContainer);

            //Subscale 6 is not done

            return ret;
        }

        private LapAssessmentContainerDto CreateFakeAssessment(long fakeId, int checkpoint)
        {
            LapAssessmentContainerDto container = new LapAssessmentContainerDto();
            container.AssessmentInstId = fakeId;
            container.OrganizationId = organizationId;
            container.StudentId = studentId;
            container.AssessmentDate = DateTime.Now;
            container.Instrument = AssessmentLevel.E_LAP;
            container.Language = 1;
            container.Checkpoint = checkpoint;

            //TODO Create subscales?
            List<LapSubscaleContainerDto> subscales = CreateFakeSubscales(fakeId);
            container.LapSubscaleContainerList = subscales;

            return container;
        }

        private void FakeData()
        {
            //Checkpoint 1
            LapAssessmentContainerDto preContainer = CreateFakeAssessment(1, 1);

            //Checkpoint 2
            LapAssessmentContainerDto midContainer = CreateFakeAssessment(2, 2);

            //Checkpoint 3
            LapAssessmentContainerDto postContainer = CreateFakeAssessment(3, 3);

            //Checkpoint 4
            LapAssessmentContainerDto ongoingContainer = CreateFakeAssessment(4, 4);

            ElapAssessmentContainerList.Add(preContainer);
            ElapAssessmentContainerList.Add(midContainer);
            ElapAssessmentContainerList.Add(postContainer);
            ElapAssessmentContainerList.Add(ongoingContainer);
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData != null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;
                studentId = commonSessionData.CurrentStudentId;

                if (studentId.HasValue)
                {
                    Common.Models.Student? student = await StudentService.GetStudentAsync(studentId);
                    if (student != null)
                    {
                        studentName = $"{student.FirstName} {student.MiddleName ?? ""} {student.LastName}".Replace("  ", " ").Trim();
                        studentBirthdate = student.BirthDate;
                    }
                }
            }

            FakeData();
        }

        // Constants for better maintainability
        private const int ELAP_COLUMN_COUNT = 6;
        private const int LAPD_COLUMN_COUNT = 13;
        private const int TOTAL_COLUMN_COUNT = 19;
        private const int ELAP_LN_SUBSCALE_SEQUENCE = 4;

        // Future feature: Low Z Scores threshold (not yet implemented)
        // TODO: Implement low Z score logic when business requirements are defined
        private const int LOW_Z_SCORE_THRESHOLD = 30; // Placeholder - update when requirements are available

        private string GetCheckpointName(int checkpoint)
        {
            return checkpoint switch
            {
                1 => "Beginning/Pre",
                2 => "Mid-year",
                3 => "End-year/Post",
                4 => "Ongoing",
                _ => string.Empty
            };
        }

        private string GetScoreClass(LapSubscaleContainerDto subscale)
        {
            if (subscale == null)
            {
                return "no-item";
            }
            if (!subscale.IsValid)
            {
                return "invalid";
            }
            if (!subscale.RawScore.HasValue)
            {
                // For E-LAP LN column, show as "started" instead of "no-item"
                if (subscale.SubscaleSequence == ELAP_LN_SUBSCALE_SEQUENCE)
                {
                    return "started";
                }
                return "no-item";
            }

            // If we have a valid score, the assessment was completed
            // TODO: Future feature - implement Low Z Scores classification
            // When implemented, use: if (subscale.ZScore < LOW_Z_SCORE_THRESHOLD) return "low-scores";

            return "completed";
        }

        private string FormatAssessmentDate(DateTime? date)
        {
            return date?.ToShortDateString() ?? string.Empty;
        }
    }
}
