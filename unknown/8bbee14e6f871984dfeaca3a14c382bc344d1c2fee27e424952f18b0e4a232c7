@page "/c4l-lesson-details"

@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.C4L.Components
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Logging

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService
@inject IStudentGroupRepository StudentGroupRepository
@inject IC4LClassroomService C4LClassroomService
@inject IC4LSessionStateService SessionStateService
@inject IC4LCmsApiService C4LCmsApiService
@inject ILogger<C4L_LessonDetailsTabs> Logger

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="lesson-details-container">
    <div class="header-container">
        <header>
            <h1>@LessonTitle</h1>
            <div class="lesson-subtitle">@LessonType</div>
            <div class="lesson-unit-info">Unit @Unit - Week @Week - Day @Day</div>
        </header>
        <button class="c4l-button c4l-primary-button" type="button" @onclick="ReturnToLessons">
            Back to Lessons
        </button>
    </div>

    <!-- Tab Navigation -->
    <div class="lesson-tabs">
        <div class="tab-item @(ActiveTab == "Overview" ? "active" : "")" @onclick="@(() => SetActiveTab("Overview"))">
            Overview
        </div>
        <div class="tab-item @(ActiveTab == "Assessment" ? "active" : "")" @onclick="@(() => SetActiveTab("Assessment"))">
            Assessment
        </div>
        <div class="tab-item @(ActiveTab == "Reflection" ? "active" : "")" @onclick="@(() => SetActiveTab("Reflection"))">
            Reflection
        </div>
    </div>

    <div class="lesson-details-content">
        @if (ActiveTab == "Overview")
        {
            <C4L_LessonDetailsOverviewComponent
                LessonContentDto="lessonContentDto"
                IsPreparationCompleted="IsPreparationCompleted"
                Language="Language"
                Unit="Unit"
                Week="Week"
                Day="Day"
                LessonTypeSequence="LessonTypeSequence"
                TitleSequence="TitleSequence"
                ClassroomId="_classroomId"
                OrganizationId="_organizationId"
                CurrentUserId="_currentUserId"
                OnPreparationStatusChanged="OnPreparationStatusChanged" />
        }
        else if (ActiveTab == "Assessment")
        {
            <C4L_LessonDetailsAssessmentComponent />
        }
        else if (ActiveTab == "Reflection")
        {
            <C4L_LessonDetailsReflectionComponent />
        }
    </div>
</div>

<style>
    .lesson-details-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 15px;
    }

    header {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    h1 {
        margin: 0;
        font-size: 28px;
        color: var(--c4l-primary-purple);
        font-weight: bold;
    }

    .lesson-subtitle {
        font-size: 16px;
    }

    .lesson-unit-info {
        font-size: 16px;
    }

    .lesson-tabs {
        display: flex;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 20px;
    }

    .tab-item {
        padding: 10px 20px;
        cursor: pointer;
        position: relative;
    }

    .tab-item.active {
        font-weight: bold;
        color: var(--c4l-primary-purple);
    }

    .tab-item.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--c4l-primary-purple);
    }

    .arrow {
        font-size: 12px;
        color: var(--c4l-primary-purple);
    }

    .lesson-details-content {
        margin-top: 20px;
    }


</style>
