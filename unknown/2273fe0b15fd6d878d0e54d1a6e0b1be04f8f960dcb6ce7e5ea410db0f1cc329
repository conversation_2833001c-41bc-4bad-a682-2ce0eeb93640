using Compass.LAP.Resources.Instruments;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Resources.SmartAssessment.Screen
{
    public abstract class LapScreenAssessment : SmartLapAssessment
    {
        // Age constants
        public const int MIN_AGE_3YO = 36;
        public const int MAX_AGE_3YO = 47;
        public const int MIN_AGE_4YO = 48;
        public const int MAX_AGE_4YO = 59;
        public const int MIN_AGE_5YO = 60;
        public const int MAX_AGE_5YO = 71;

        // Private fields
        private int? type;
        private int? totalScore;
        private string? result;

        // Properties
        public int? Type
        {
            get { return type; }
            set { type = value; }
        }

        public int? TotalScore
        {
            get { return totalScore; }
            set { totalScore = value; }
        }

        public string? Result
        {
            get { return result; }
            set { result = value; }
        }

        public override Milestone GetNextItem(Milestone previousItem, int? previousScore, ICollection<Milestone> milestones)
        {
            List<Milestone> milestonesList = (List<Milestone>)milestones;
            int idx = milestonesList.IndexOf(previousItem);
            // always get next item
            idx++;
            Milestone? ret = null;
            if (idx != milestones.Count)
            {
                ret = milestonesList[idx];
            }

            return ret;
        }

        protected override void UpdateItemBeforeAutoScore(AssessmentItem item, Skill skill, List<Milestone> milestones, int? lastItemSequence)
        {
            CalculateTotalScore();
        }

        private void CalculateTotalScore()
        {
            int score = 0;
            foreach (AssessmentItem item in AssessmentItems)
            {
                if (item.Value == Skill.MASTERED)
                {
                    score++;
                }
            }
            TotalScore = score;
        }

        protected override void CopyAdditionalData(SmartLapAssessment result)
        {
            TotalScore = ((LapScreenAssessment)result).TotalScore;
        }

        public string GetTypeString()
        {
            string ret = "3";
            switch (Type)
            {
                case AssessmentLevel.LAP_Screen_4YO:
                    ret = "4";
                    break;
                case AssessmentLevel.LAP_Screen_5YO:
                    ret = "5";
                    break;
            }
            return ret;
        }

        public int GetTypeFromString(string type)
        {
            int ret = AssessmentLevel.LAP_Screen_3YO;
            if (type.Equals("4"))
            {
                ret = AssessmentLevel.LAP_Screen_4YO;
            }
            else if (type.Equals("5"))
            {
                ret = AssessmentLevel.LAP_Screen_5YO;
            }
            return ret;
        }

        // Abstract methods that need to be implemented by derived classes
        public override Milestone GetStartingPoint(ICollection<Milestone> milestones)
        {
            // Screen assessments typically start from the first item
            Milestone? firstItem = FindFirstItem(milestones);
            return firstItem ?? throw new InvalidOperationException("No milestones available");
        }

        public override Milestone? FindFirstItem(ICollection<Milestone> milestones)
        {
            List<Milestone> milestonesList = milestones.ToList();
            return milestonesList.Count > 0 ? milestonesList[0] : null;
        }
    }
}
