using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Services
{
    public interface IC4LLessonPreparationCompletedService
    {
        /// <summary>
        /// Gets a C4LLessonPreparationCompleted record by preparation ID and school year
        /// Note, if school year is null, the current school year is used
        /// </summary>
        Task<C4LLessonPreparationCompleted?> GetAsync(long preparationId, int? schoolYear, long c4lClassroomId);

        /// <summary>
        /// Gets a C4LLessonPreparationCompleted record by query by example preparationCompleted
        /// Note, if preparationCompleted.schoolYear is null, the current school year is used
        /// </summary>
        Task<C4LLessonPreparationCompleted?> GetAsync(C4LLessonPreparationCompleted? preparationCompleted);

        /// <summary>
        /// Creates or updates a C4LLessonPreparationCompleted record
        /// </summary>
        Task<C4LLessonPreparationCompleted> SaveCompletionStatusAsync(long preparationId, int? schoolYear, long c4lClassroomId, bool isCompleted);

        /// <summary>
        /// Creates or updates a C4LLessonPreparationCompleted record
        /// </summary>
        Task<C4LLessonPreparationCompleted> SaveCompletionStatusAsync(C4LLessonPreparationCompleted? preparationCompleted, bool isCompleted);
    }
}
