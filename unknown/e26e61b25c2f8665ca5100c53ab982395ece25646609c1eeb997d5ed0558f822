using Compass.LAP.Models;

namespace Compass.LAP.Interfaces.Services
{
    /// <summary>
    /// Interface for a service that manages session state for LAP components
    /// </summary>
    public interface ILAPSessionStateService
    {
        /// <summary>
        /// The current assessment ID
        /// </summary>
        long AssessmentId { get; set; }

        /// <summary>
        /// Set session data in a single operation (recommended for better performance)
        /// </summary>
        Task SetSessionDataAsync(Models.LAPSessionData sessionData);

        /// <summary>
        /// Get session data in a single operation (recommended for better performance)
        /// </summary>
        Task<Models.LAPSessionData> GetSessionDataAsync();

        /// <summary>
        /// Clear all session data
        /// </summary>
        Task ClearSessionDataAsync();
    }
}
