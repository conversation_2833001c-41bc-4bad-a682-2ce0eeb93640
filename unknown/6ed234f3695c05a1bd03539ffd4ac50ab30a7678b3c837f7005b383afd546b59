using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class LAPScreenItemRepository : ILAPScreenItemRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public LAPScreenItemRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<LAPScreenItem> SaveAsync(LAPScreenItem lapScreenItem)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (lapScreenItem.Id == 0)
                {
                    // Create new record
                    _dbContext.LAPScreenItems.Add(lapScreenItem);
                }
                else
                {
                    // Update existing record
                    _dbContext.LAPScreenItems.Update(lapScreenItem);
                }

                await _dbContext.SaveChangesAsync();
                return lapScreenItem;
            }
        }

        public async Task<List<LAPScreenItem>> GetItemsByAssessmentAsync(long? organizationId, long? assessmentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<LAPScreenItem> items = await _dbContext.LAPScreenItems
                    .Where(i => i.OrganizationId == organizationId.Value
                             && i.AssessmentId == assessmentId.Value)
                    .ToListAsync();

                return items;
            }
        }

        public async Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId)
        {
            string sqlQuery = @"SELECT itm.InstID AS Id, itm.AchieveDate, itm.AssessDate, itm.AssessmentInstID, a.ChronologicalAge, itm.CustomerInstID,
		                                lkup_sub.Domain, lkup_sub.DomainSequence, lkup_itm.SubscaleID + lkup_itm.Sequence AS ItemID,
		                                a.SchoolYear, itm.SelectedItems, itm.Sequence, lkup_sub.Subscale, lkup_itm.SubscaleID, lkup_sub.SubscaleSequence,
		                                itm.UserComment, itm.Value, itm.ChildInstID, itm.ObserverInstID, itm.ItemStaticID, itm.SubscaleStaticID
                                FROM lap_lap_screen_items AS itm
                                INNER JOIN lap_assessments AS a
	                                ON a.InstID = itm.AssessmentInstID
		                                AND a.CustomerInstID = itm.CustomerInstID
                                INNER JOIN lap_subscales_lookup AS lkup_sub
	                                ON lkup_sub.StaticID = itm.SubscaleStaticID
                                INNER JOIN lap_items_lookup AS lkup_itm
	                                ON lkup_itm.StaticId = itm.ItemStaticID
                                INNER JOIN lap_lap_screen_observation_items AS obs
	                                ON obs.SourceItemInstID = itm.InstID
                                WHERE itm.CustomerInstID = {0}
	                                AND itm.AssessmentInstID = {1}";

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<AssessmentItem> resultList = await _dbContext.AssessmentItems.FromSqlRaw(sqlQuery, organizationId, assessmentId)
                    .Select(i => new AssessmentItem
                    {
                        Id = i.Id,
                        AchieveDate = i.AchieveDate,
                        AssessDate = i.AssessDate,
                        AssessmentInstID = i.AssessmentInstID,
                        ChronologicalAge = i.ChronologicalAge,
                        CustomerInstID = i.CustomerInstID,
                        Domain = i.Domain,
                        DomainSequence = i.DomainSequence,
                        Instrument = i.Instrument,
                        ItemID = i.ItemID,
                        SchoolYear = i.SchoolYear,
                        SelectedItems = i.SelectedItems,
                        Sequence = i.Sequence,
                        Subscale = i.Subscale,
                        SubscaleID = i.SubscaleID,
                        SubscaleSequence = i.SubscaleSequence,
                        UserComment = i.UserComment,
                        Value = i.Value,
                        ChildInstID = i.ChildInstID,
                        ObserverInstID = i.ObserverInstID,
                        ItemStaticID = i.ItemStaticID,
                        SubscaleStaticID = i.SubscaleStaticID
                    })
                    .ToListAsync();

                return resultList;
            }
        }
    }
}
