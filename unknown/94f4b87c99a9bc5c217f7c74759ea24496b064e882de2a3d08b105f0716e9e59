using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Resources.SmartAssessment.Observation
{
    public class ProgressObservationItem
    {
        public long Id { get; set; }
        public long? ObservationInstID { get; set; }
        public long? ChildInstID { get; set; }
        public long? ItemStaticID { get; set; }
        public long? SubscaleStaticID { get; set; }
        public long? AssessmentItemInstID { get; set; }
        public long? CustomerInstID { get; set; }
        public int? Instrument { get; set; }
        public bool? HasAffectedFinalizedSubscale { get; set; }
        public long? SourceSubscaleInstID { get; set; }
        public long? SourceItemInstID { get; set; }
        public int? CrossScore { get; set; }

        public Milestone? StaticItem { get; set; }
    }
}
