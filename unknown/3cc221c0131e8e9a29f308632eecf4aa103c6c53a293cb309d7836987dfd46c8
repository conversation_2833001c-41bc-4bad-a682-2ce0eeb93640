﻿using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class StaticSubscaleRepository : IStaticSubscaleRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public StaticSubscaleRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<List<LAPSubScalesLookup>> GetStaticSubscales(int? instrument, int? language)
        {
            if (instrument is null)
            {
                throw new ArgumentNullException(nameof(instrument));
            }

            if (language is null)
            {
                throw new ArgumentNullException(nameof(language));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<LAPSubScalesLookup> subscales = await _dbContext.LAPSubScalesLookups
                    .Where(s => s.Instrument == instrument.Value && s.Language == language.Value)
                    .OrderBy(s => s.DomainSequence)
                    .ThenBy(s => s.SubscaleSequence)
                    .ToListAsync();

                return subscales;
            }
        }
    }
}
