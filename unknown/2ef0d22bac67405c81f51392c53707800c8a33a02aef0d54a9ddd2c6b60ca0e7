.assessment-summary {
    --border-radius: var(--button-border-radius);
    --cell-size: 3rem;
    --checkpoint-width: 8rem;
    --section-gap: 1.5rem;

    --elap-column-count: 6;
    --lapd-column-count: 13;
    --elap-internal-gaps: 0.3125rem;
    --lapd-internal-gaps: 0.75rem;

    --header-light-purple: hsl(278 37.3% 87.5%);
    --date-color: var(--c4l-tertiary-yellow);

    --completed-color: hsl(152 69% 30%);
    --started-color: hsl(190 80% 20%);
    --invalid-color: var(--c4l-danger);
    --low-scores-color: hsl(32 100% 30%);
    --no-item-color: hsl(0 0% 45%);

    --completed-border: hsl(152 69% 20%);
    --started-border: hsl(190 80% 10%);
    --invalid-border: var(--c4l-alert-danger);
    --low-scores-border: hsl(32 100% 20%);
    --no-item-border: hsl(0 0% 35%);
}

.assessment-summary-title {
    margin-block-start: -0.5rem;
}

.assessment-summary-wrapper {
    margin-block: 6rem 2rem;
    width: min(100%, 1600px);
    margin-inline: auto;
}

.info-legend-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-block-end: 0.625rem;
}

.student-info {
    padding: 1rem;
    border-radius: var(--border-radius);
}

.student-name, .student-dob {
    font-size: 1rem;
    font-weight: 600;
    color: var(--c4l-primary-purple);
    margin-block-end: 0.3125rem;
}

.student-dob {
    margin-block-end: 0;
}

.progress-legend {
    display: flex;
    gap: 1.25rem;
    align-items: center;
    padding: 0.625rem;
    border-radius: var(--border-radius);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-box {
    width: 1rem;
    height: 1rem;
    border-radius: 0.125rem;
}

.legend-box.completed {
    background-color: var(--completed-color);
}

.legend-box.started {
    background-color: var(--started-color);
}

.legend-box.invalid {
    background-color: var(--invalid-color);
}

.legend-box.low-scores {
    background-color: var(--low-scores-color);
}

.legend-box.no-item {
    background-color: var(--no-item-color);
}

.assessment-table {
    font-size: 0.875rem;
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid var(--c4l-primary-purple);
    border-radius: var(--border-radius);
    width: fit-content;
    margin: 0 auto;
    position: relative;
    padding: 0.5rem;
    overflow: visible;
}

.assessment-table::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: transparent;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    z-index: -1;
    pointer-events: none;
}

.assessment-table:hover::before {
    opacity: 1;
}

.table-header {
    display: flex;
    align-items: stretch;
    gap: var(--section-gap);
    background-color: var(--header-light-purple);
    margin: -0.5rem -0.5rem 0 -0.5rem;
    padding: 0.5rem 0.5rem 0 0.5rem;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.checkpoint-column {
    width: var(--checkpoint-width);
    display: flex;
    flex-direction: column;
}

.elap-section, .lapd-section {
    display: flex;
    flex-direction: column;
}

.elap-section {
    width: calc(var(--elap-column-count) * var(--cell-size) + var(--elap-internal-gaps));
    flex-shrink: 0;
}

.lapd-section {
    width: calc(var(--lapd-column-count) * var(--cell-size) + var(--lapd-internal-gaps));
    flex-shrink: 0;
}

.header-cell, .section-header {
    background-color: var(--header-light-purple);
    color: var(--c4l-primary-purple);
    font-weight: 600;
    text-align: center;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkpoint-header {
    flex: 1;
}

.subheader-row {
    display: flex;
    gap: 0.0625rem;
}

.subheader-cell {
    background-color: var(--header-light-purple);
    color: var(--c4l-primary-purple);
    font-weight: 600;
    text-align: center;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: var(--cell-size);
}

.assessment-row {
    display: flex;
    flex-direction: column;
    gap: 0.0625rem;
}

.date-row, .data-row {
    display: flex;
    gap: var(--section-gap);
    background-color: var(--white);
}

.date-cell {
    font-size: 0.7rem;
    color: var(--date-color);
    font-weight: 600;
    padding: 0.125rem 0.25rem;
    background-color: var(--white);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2.5rem;
}

.checkpoint-cell {
    background-color: var(--white);
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2rem;
}

.score-row {
    display: flex;
    gap: 0.0625rem;
}

.score-button {
    background-color: var(--white);
    border: 0.0625rem solid transparent;
    border-radius: 0.1875rem;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    min-height: 2rem;
    padding: 0.25rem;
    cursor: pointer;
    transition:
        box-shadow var(--transition-speed) ease,
        background-color var(--transition-speed) ease;
    box-shadow:
        0 0.125rem 0.25rem rgba(0, 0, 0, 0.15),
        0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1),
        inset 0 0.0625rem 0 rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: var(--cell-size);
}

.score-button:focus {
    outline: 0.1875rem solid var(--c4l-primary-purple);
    outline-offset: 0.125rem;
    box-shadow:
        0 0.25rem 0.375rem rgba(0, 0, 0, 0.1),
        0 0.125rem 0.25rem rgba(0, 0, 0, 0.06),
        inset 0 0.0625rem 0 rgba(255, 255, 255, 0.4),
        0 0 0 0.25rem rgba(98, 187, 70, 0.25);
    z-index: 1;
    position: relative;
}

.checkpoint-cell:focus {
    outline: 0.1875rem solid var(--c4l-primary-purple);
    outline-offset: 0.125rem;
    background-color: var(--header-light-purple);
}

.score-button.completed {
    background: linear-gradient(145deg, var(--completed-color), var(--completed-border));
    border-color: var(--completed-border);
}

.score-button.started {
    background: linear-gradient(145deg, var(--started-color), var(--started-border));
    border-color: var(--started-border);
}

.score-button.invalid {
    background: linear-gradient(145deg, var(--invalid-color), var(--invalid-border));
    border-color: var(--invalid-border);
}

.score-button.low-scores {
    background: linear-gradient(145deg, var(--low-scores-color), var(--low-scores-border));
    border-color: var(--low-scores-border);
}

.score-button.no-item {
    background: linear-gradient(145deg, var(--no-item-color), var(--no-item-border));
    border-color: var(--no-item-border);
    color: var(--white);
}

@media (prefers-contrast: high) {
    .assessment-summary {
        --completed-color: hsl(152 69% 25%);
        --started-color: hsl(190 80% 15%);
        --low-scores-color: hsl(32 100% 25%);
        --no-item-color: hsl(0 0% 40%);
    }

    .score-button:focus {
        outline: 0.25rem solid var(--c4l-primary-purple);
        outline-offset: 0.1875rem;
    }
}
