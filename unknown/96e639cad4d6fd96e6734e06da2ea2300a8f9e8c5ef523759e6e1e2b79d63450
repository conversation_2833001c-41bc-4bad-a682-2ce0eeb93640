using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class LAP3SubScaleRepository : ILAP3SubScaleRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public LAP3SubScaleRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<LAP3SubScale> SaveAsync(LAP3SubScale lap3SubScale)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (lap3SubScale.Id == 0)
                {
                    // Create new record
                    _dbContext.LAP3SubScales.Add(lap3SubScale);
                }
                else
                {
                    // Update existing record
                    _dbContext.LAP3SubScales.Update(lap3SubScale);
                }

                await _dbContext.SaveChangesAsync();
                return lap3SubScale;
            }
        }

        public async Task<LAP3SubScale?> GetByIdAsync(long? organizationId, long? id, long? assessmentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.LAP3SubScales
                    .FirstOrDefaultAsync(l => l.Id == id.Value && l.OrganizationId == organizationId.Value && l.AssessmentId == assessmentId.Value);
            }
        }
    }
}
