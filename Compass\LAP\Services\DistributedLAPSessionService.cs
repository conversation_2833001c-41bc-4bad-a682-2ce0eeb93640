using Compass.Common.Data;
using Compass.Common.Services;
using Compass.LAP.Interfaces.Services;
using Compass.LAP.Models;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Compass.LAP.Services
{
    /// <summary>
    /// Distributed session state service for LAP components using Redis cache
    /// </summary>
    public class DistributedLAPSessionService : ILAPSessionStateService
    {
        private readonly IDistributedCache _cache;
        private readonly UserAccessor _userAccessor;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);
        
        private const string SESSION_KEY_PREFIX = "lap:session:";

        public DistributedLAPSessionService(IDistributedCache cache, UserAccessor userAccessor)
        {
            _cache = cache;
            _userAccessor = userAccessor;
        }

        #region Individual Property Access

        /// <summary>
        /// The current assessment ID
        /// </summary>
        public long AssessmentId
        {
            get => GetAssessmentIdAsync().GetAwaiter().GetResult();
            set => SetAssessmentIdAsync(value).GetAwaiter().GetResult();
        }

        private async Task<long> GetAssessmentIdAsync()
        {
            Models.LAPSessionData sessionData = await GetSessionDataAsync();
            return sessionData.AssessmentId;
        }

        private async Task SetAssessmentIdAsync(long assessmentId)
        {
            Models.LAPSessionData sessionData = await GetSessionDataAsync();
            sessionData.AssessmentId = assessmentId;
            await SetSessionDataAsync(sessionData);
        }

        #endregion

        #region Batch Operations (Recommended for better performance)

        /// <summary>
        /// Set session data in a single operation
        /// </summary>
        public async Task SetSessionDataAsync(Models.LAPSessionData sessionData)
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return;

            string key = SESSION_KEY_PREFIX + userId;
            string serializedData = JsonSerializer.Serialize(sessionData);
            DistributedCacheEntryOptions options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _defaultExpiration
            };

            await _cache.SetStringAsync(key, serializedData, options);
        }

        /// <summary>
        /// Get session data in a single operation
        /// </summary>
        public async Task<Models.LAPSessionData> GetSessionDataAsync()
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return new Models.LAPSessionData();

            string key = SESSION_KEY_PREFIX + userId;
            string? serializedData = await _cache.GetStringAsync(key);

            return serializedData == null
                ? new Models.LAPSessionData()
                : JsonSerializer.Deserialize<Models.LAPSessionData>(serializedData) ?? new Models.LAPSessionData();
        }

        /// <summary>
        /// Clear all session data
        /// </summary>
        public async Task ClearSessionDataAsync()
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return;

            string key = SESSION_KEY_PREFIX + userId;
            await _cache.RemoveAsync(key);
        }

        #endregion

        #region Private Helper Methods

        private async Task<string?> GetCurrentUserIdAsync()
        {
            (ApplicationUser?, string?) result = await _userAccessor.GetUserAndIdAsync();
            return result.Item2;
        }

        #endregion
    }
}
