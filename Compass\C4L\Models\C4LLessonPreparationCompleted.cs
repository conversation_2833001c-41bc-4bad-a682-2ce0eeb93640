﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_lesson_preparations_completed")]
    public class C4LLessonPreparationCompleted
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("schoolyear")]
        public int Schoolyear { get; set; }

        [Column("classroom_id")]
        public long C4LClassroomId { get; set; }

        [Column("unit")]
        public int Unit { get; set; }

        [Column("lesson_week")]
        public int Week { get; set; }

        [Column("lesson_day")]
        public int Day { get; set; }

        [Column("lesson_type_sequence")]
        public int LessonTypeSequence { get; set; }

        [Column("title_sequence")]
        public int TitleSequence { get; set; }

        [Column("date_completed")]
        public DateTime? DateCompleted { get; set; }
    }
}
