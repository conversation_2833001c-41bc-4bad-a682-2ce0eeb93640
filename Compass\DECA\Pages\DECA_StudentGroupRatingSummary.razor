﻿@page "/deca-student-group-rating-summary"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.DECA.DTOs
@using Compass.Common.Controls.Generic
@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<h1 class="page-title deca-rating-summary-title horizontal-line">Student Group Rating Summary</h1>

<section class="roster-header d-flex justify-content-between align-items-center flex-column flex-md-row align-items-start align-items-md-center">
    <h2 class="class-roster-title font-weight-600">Class Roster</h2>

    <form class="search-container d-flex" role="search">
        <input type="search" class="search-input" placeholder="Search" @bind="searchText" @bind:event="oninput" />
    </form>
</section>

<LoaderComponent IsLoading="isLoading">
    @if (studentRatings != null && studentRatings.Any())
    {
        <section class="class-roster">
            @foreach (IGrouping<string, StudentGroupRatingDto> studentGroup in GetGroupedStudentRatings())
            {
                <article class="roster-row">
                    <div class="student-info-bar font-weight-400 d-flex justify-content-between align-items-center flex-column flex-md-row align-items-start align-items-md-center" @onclick="() => ToggleStudent(studentGroup.Key)" tabindex="0" role="button" aria-expanded="@(expandedStudents.Contains(studentGroup.Key) ? "true" : "false")" aria-label="Toggle details for @studentGroup.Key">
                        <address class="student-info d-flex align-items-center">
                            <p class="student-name font-weight-700">@studentGroup.Key</p>
                        </address>
                        <aside class="student-summary d-flex align-items-center">
                            <button class="view-child-btn d-flex align-items-center" type="button" @onclick="async () => await NavigateToStudentRatingsFromInfoBar(studentGroup)" @onclick:stopPropagation="true">
                                <i class="bi bi-eye" aria-hidden="true"></i>
                                View Child
                            </button>
                        </aside>
                    </div>
                    <section class="rating-details @(expandedStudents.Contains(studentGroup.Key) ? "expanded d-flex flex-column align-items-start" : "")" aria-label="Student rating details">
                        <h3 class="visually-hidden">Rating Data</h3>

                        <div class="ratings-grid-top font-weight-500 d-flex">
                            <span class="grid-cell font-weight-500 d-flex align-items-center justify-content-center">Rating Date</span>
                            <span class="grid-cell font-weight-500 d-flex align-items-center justify-content-center">Rating Period</span>
                            <span class="grid-cell font-weight-500 d-flex align-items-center justify-content-center">Rating Type</span>
                            <span class="grid-cell font-weight-500 d-flex align-items-center justify-content-center">Teacher or Parent</span>
                            <section class="grid-group d-flex flex-column">
                                <p class="grid-group-title font-weight-500">Raw Score</p>
                                <span class="grid-subgroup d-flex">
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">IN</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">SR</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">AT</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">TPF</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">BC</span>
                                </span>
                            </section>
                            <section class="grid-group d-flex flex-column">
                                <p class="grid-group-title font-weight-500">T-Score</p>
                                <span class="grid-subgroup d-flex">
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">IN</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">SR</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">AT</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">TPF</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">BC</span>
                                </span>
                            </section>
                            <section class="grid-group d-flex flex-column">
                                <p class="grid-group-title font-weight-500">Percentile</p>
                                <span class="grid-subgroup d-flex">
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">IN</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">SR</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">AT</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">TPF</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">BC</span>
                                </span>
                            </section>
                            <section class="grid-group d-flex flex-column">
                                <p class="grid-group-title font-weight-500">Description</p>
                                <span class="grid-subgroup d-flex">
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">IN</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">SR</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">AT</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">TPF</span>
                                    <span class="grid-subcell font-weight-500 d-flex align-items-center justify-content-center">BC</span>
                                </span>
                            </section>
                            <span class="grid-cell font-weight-500 d-flex align-items-center justify-content-center">Actions</span>
                        </div>
                        
                        <div class="ratings-grid-content d-flex flex-column">
                            @foreach (StudentGroupRatingDto rating in studentGroup)
                            {
                                <article class="rating-data-row d-flex">
                                    <time class="data-cell d-flex align-items-center justify-content-center" datetime="@rating.RatingDate?.ToString("yyyy-MM-dd")">@GetFormattedDate(rating.RatingDate)</time>
                                    <span class="data-cell d-flex align-items-center justify-content-center">@GetSafeString(rating.RatingPeriod)</span>
                                    <span class="data-cell d-flex align-items-center justify-content-center">@GetSafeString(rating.RecordForm)</span>
                                    <span class="data-cell d-flex align-items-center justify-content-center">@GetSafeString(rating.RaterType)</span>
                                    <div class="data-group d-flex">
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.InRaw)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.ScRaw)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.ArRaw)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.TpfRaw)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.BcRaw)</span>
                                    </div>
                                    <div class="data-group d-flex">
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.InTScore)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.ScTScore)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.ArTScore)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.TpfTScore)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.BcTScore)</span>
                                    </div>
                                    <div class="data-group d-flex">
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.InPercentile)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.ScPercentile)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.ArPercentile)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.TpfPercentile)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeNumber(rating.BcPercentile)</span>
                                    </div>
                                    <div class="data-group d-flex">
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeString(rating.InDescription)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeString(rating.ScDescription)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeString(rating.AtDescription)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeString(rating.TpfDescription)</span>
                                        <span class="data-subcell d-flex align-items-center justify-content-center">@GetSafeString(rating.BcDescription)</span>
                                    </div>
                                    <span class="data-cell d-flex align-items-center justify-content-center">
                                        <span class="rating-actions d-flex justify-content-center">
                                            <button class="action-btn d-flex align-items-center justify-content-center" type="button" title="Edit rating" aria-label="Edit rating for @GetStudentName(rating.FirstName, rating.LastName)" @onclick="async () => await NavigateToEditRating(rating.StudentId, rating.RatingId)">
                                                <i class="bi bi-pencil" aria-hidden="true"></i>
                                            </button>
                                            <button class="action-btn d-flex align-items-center justify-content-center" type="button" title="Refresh rating" aria-label="Refresh rating for @GetStudentName(rating.FirstName, rating.LastName)">
                                                <i class="bi bi-arrow-clockwise" aria-hidden="true"></i>
                                            </button>
                                        </span>
                                    </span>
                                </article>
                            }
                        </div>
                    </section>
                </article>
            }
        </section>
    }
    else
    {
        <NoTableDataMessage MessageText="No student ratings found for this student group." />
    }
</LoaderComponent>