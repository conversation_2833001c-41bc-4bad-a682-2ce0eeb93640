﻿@using Compass.Common.DTOs.StudentGroup
@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject NavigationManager NavigationManager
@inject UserAccessor UserAccessor
@inject IStudentService StudentService

<EditForm Model="Input" FormName="formStudent" OnValidSubmit="SaveStudentAsync" class="c4l-form studentgroup-edit-form mb-5">
    <h3 class="c4l-form-heading">@($"Edit Student: {Input.FirstName} {Input.LastName}")</h3>

    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>

    <FieldComponent Label="First Name" LabelFor="student-firstname">
        <Control>
            <InputText Id="student-firstname" @bind-Value="Input.FirstName" Required="true" class="form-control"></InputText>
        </Control>
    </FieldComponent>

    <FieldComponent Label="Middle Name" LabelFor="student-middlename">
        <Control>
            <InputText Id="student-middlename" @bind-Value="Input.MiddleName" class="form-control"></InputText>
        </Control>
    </FieldComponent>

    <FieldComponent Label="Last Name" LabelFor="student-lastname">
        <Control>
            <InputText Id="student-lastname" @bind-Value="Input.LastName" Required="true" class="form-control"></InputText>
        </Control>
    </FieldComponent>

    <FieldComponent Label="School Id" LabelFor="student-schoolid">
        <Control>
            <InputText Id="student-schoolid" @bind-Value="Input.SchoolId" class="form-control"></InputText>
        </Control>
        <ValidationControl>
            @if (showDuplicateSchoolIdError)
            {
                <div class="text-danger">@errorMessage</div>
            }
        </ValidationControl>
    </FieldComponent>

    <FieldComponent Label="Gender">
        <Control>
            <InputRadioGroup @bind-Value="Input.Gender">
                <div class="form-check">
                    <InputRadio Value="@("M")" class="form-check-input" id="male-radio" />
                    <label class="form-check-label" for="male-radio">Male</label>
                </div>
                <div class="form-check">
                    <InputRadio Value="@("F")" class="form-check-input" id="female-radio" />
                    <label class="form-check-label" for="female-radio">Female</label>
                </div>
            </InputRadioGroup>
        </Control>
    </FieldComponent>

    <FieldComponent Label="Birth Date" LabelFor="student-birthday">
        <Control>
            <InputDate Id="student-birthday" @bind-Value="Input.BirthDate" class="form-control" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Enroll Date" LabelFor="student-enroll-date">
        <Control>
            <InputDate Id="student-enroll-date" @bind-Value="Input.EnrollDate" class="form-control" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Hispanic Latino" LabelFor="student-hispanic-latino">
        <Control>
            <InputCheckbox Id="student-hispanic-latino" class="form-check-input" @bind-Value="Input.HispanicLatino" />
        </Control>
    </FieldComponent>

    @foreach(StudentRace race in studentRaceList)
    {
        var formattedRaceId = SanitizeRaceId(race.Race);

        <FieldComponent Label="@race.Race" LabelFor="@formattedRaceId">
            <Control>
                <input 
                    type="checkbox" 
                    id="@formattedRaceId"
                    class="form-check-input"
                    value="@race.Id" 
                    checked="@(Input.SelectedRaces != null && Input.SelectedRaces.Contains(race.Id))" 
                    @onchange="(e) => ToggleRaceSelections(race.Id, e.Value as bool?)" 
                />
            </Control>
        </FieldComponent>
    }

    <FieldComponent Label="Language" LabelFor="student-language">
        <Control>
            <InputSelect id="student-language" @bind-Value="Input.Language">
                <option value="" disabled>-- Language --</option>
                @foreach (StudentLanguage language in studentLanguageList)
                {
                    <option value="@language.Id">@language.Language</option>
                }
            </InputSelect>
        </Control>
        <ValidationControl>
            <ValidationMessage For="@(() => Input.Language)" />
        </ValidationControl>
    </FieldComponent>

    <FieldComponent Label="Free/Reduced Lunch" LabelFor="student-free-reduced-lunch">
        <Control>
            <InputCheckbox Id="student-free-reduced-lunch" class="form-check-input" @bind-Value="Input.SchoolLunch" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Dual Language Learner" LabelFor="student-dual-language-learner">
        <Control>
            <InputCheckbox Id="student-dual-language-learner" class="form-check-input" @bind-Value="Input.DualLanguage" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="IFSP/IEP" LabelFor="student-ifsp-iep">
        <Control>
            <InputCheckbox Id="student-ifsp-iep" class="form-check-input" @bind-Value="Input.IEPIFSP" />
        </Control>
    </FieldComponent>

    <FieldComponent Label="Has Disability?" LabelFor="student-has-disability">
        <Control>
            <InputCheckbox Id="student-has-disability" class="form-check-input" @bind-Value="Input.HasDisability" />
        </Control>
    </FieldComponent>

    @if (Input.HasDisability)
    {
        <FieldComponent Label="Disability" LabelFor="student-disability">
            <Control>
                <InputText Id="student-disability" @bind-Value="Input.Disability" Required="true" class="form-control"></InputText>
            </Control>
        </FieldComponent>
    }

    <div class="form-submit-buttons-wrapper">
        <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">Update Student</button>
        <button class="c4l-button c4l-form-button c4l-secondary-button" @onclick="() => OnCancelClick()">Cancel</button>

        @if (showSuccessMessage)
        {
            <div class="alert alert-success fade show" role="alert">
                @successMessage
            </div>
        }
    </div>
</EditForm>

<div class="c4l-search-table-wrapper mt-0">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <div class="c4l-table-scroll-wrapper">
        <div class="c4l-table-wrapper">
            <div class="c4l-table-headings-wrapper">
                <h6 class="c4l-table-heading">@studentGroupHierarchy</h6>
                @if (!string.IsNullOrWhiteSpace(entity1Hierarchy))
                {
                    <h6 class="c4l-table-heading">@entity1Hierarchy</h6>
                }
                @if (!string.IsNullOrWhiteSpace(entity2Hierarchy))
                {
                    <h6 class="c4l-table-heading">@entity2Hierarchy</h6>
                }
                @if (!string.IsNullOrWhiteSpace(entity3Hierarchy))
                {
                    <h6 class="c4l-table-heading">@entity3Hierarchy</h6>
                }
                @if (!string.IsNullOrWhiteSpace(siteHierarchy))
                {
                    <h6 class="c4l-table-heading">@siteHierarchy</h6>
                }
                <h6>Remove Student?</h6>
            </div>

            @foreach (StudentGroupListDisplayDto studentGroup in assignedStudentGroupResults)
            {
                <div class="c4l-table-result-wrapper">
                    <p class="c4l-table-result-item">@studentGroup.Name</p>
                    @if(!string.IsNullOrWhiteSpace(studentGroup.Entity1Name))
                    {
                        <p class="c4l-table-result-item">@studentGroup.Entity1Name</p>
                    }
                    @if(!string.IsNullOrWhiteSpace(studentGroup.Entity2Name))
                    {
                        <p class="c4l-table-result-item">@studentGroup.Entity2Name</p>
                    }
                    @if(!string.IsNullOrWhiteSpace(studentGroup.Entity3Name))
                    {
                        <p class="c4l-table-result-item">@studentGroup.Entity3Name</p>
                    }
                    @if(!string.IsNullOrWhiteSpace(studentGroup.SiteName))
                    {
                        <p class="c4l-table-result-item">@studentGroup.SiteName</p>
                    }
                    <button class="c4l-button c4l-danger-button" @onclick="() => OnRemoveClick(studentGroup.Id)">Remove</button>
                </div>
            }
        </div>
    </div>
</div>

<div class="c4l-pagination-wrapper">
    <div class="c4l-pagination-buttons-wrapper">
        <div class="buttons-wrapper">
            <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                    @onclick="() => OnPreviousClicked()"
                    disabled="@(currentPage <= 1)">
                Previous
            </button>

            <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                    @onclick="() => OnNextClicked()"
                    disabled="@(currentPage >= maxPages)">
                Next
            </button>

        </div>

        <div>
            <button class="c4l-button c4l-primary-button" type="button" @onclick="() => OnAssignClick()">Assign</button>
        </div>
    </div>

    <div class="page-count-wrapper font-weight-500">
        <span class="current-page-number">@currentPage</span> of @maxPages
    </div>
</div>

<DialogBox 
    Title="Attention"
    Message=@dialogMessage
    IsVisible="@isRemoveDialogVisible"
    DialogResult="OnRemoveDialogResult" />

<StudentAssignBox 
    IsVisible=@isAssignStudentGroupVisible
    AssignResult="OnAssignStudentGroupResult" />
