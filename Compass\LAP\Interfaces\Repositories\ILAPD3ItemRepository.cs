using Compass.LAP.Models;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Interfaces.Repositories
{
    public interface ILAPD3ItemRepository
    {
        Task<LAPD3Item> SaveAsync(LAPD3Item lapd3Item);
        Task<List<LAPD3Item>> GetItemsBySubscaleAsync(long? organizationId, long? assessmentId, long? subscaleId);
        Task<List<AssessmentItem>> GetAssessmentItemsAsync(long? organizationId, long? assessmentId, long? subscaleId);
    }
}
