﻿using Compass.Common.Data;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Compass.LAP.DTOs;
using Compass.LAP.Interfaces.Services;
using Compass.LAP.Resources.SmartAssessment.Criterion;
using Compass.LAP.Resources.SmartAssessment.Primary;
using Microsoft.AspNetCore.Components;

namespace Compass.LAP.Pages.Student.Assessment.Entry
{
    public partial class LAP_GuidedAssessmentEntry
    {
        [Inject]
        public required UserAccessor UserAccessor { get; set; }
        [Inject]
        public required UserSessionService UserSessionService { get; set; }
        [Inject]
        public required ILAPSessionStateService LAPSessionStateService { get; set; }
        [Inject]
        public required ILapAssessmentService LapAssessmentService { get; set; }

        private int chronAge;
        private int basal;
        private int ceiling;
        private DateTime assessmentDate;

        private CriterionAssessment? smartAssessment;
        private Milestone? currentMilestone;
        private Milestone? previousMilestone;
        private List<Milestone> milestones = new List<Milestone>();
        private List<Milestone> subscaleCol = new List<Milestone>();

        private int previousScore;
        private long? currentSubscaleID;
        private int startingPoint;

        private string currentItemNote = string.Empty;
        private int itemNoteSequence;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            await GetCommonSessionData();

            LapAssessmentEntryDto dto = await LapAssessmentService.GetLapAssessmentForEntry(1, 1, null, null, 1, 10, 1, 1);

            milestones = dto.LapStaticItems;
            subscaleCol = dto.LapStaticSubscales;

            currentSubscaleID = dto.CurrentSubscaleInstID;

            if (dto.SmartAssessment is CriterionAssessment criterionAssessment)
            {
                smartAssessment = criterionAssessment;
            }
            else
            {
                //TODO SCREEN ITEM SHOULD NOT BE HERE
            }

            //TODO Get smart assessment
        }
    }
}
