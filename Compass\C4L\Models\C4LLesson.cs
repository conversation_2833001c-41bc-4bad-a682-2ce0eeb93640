using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4L_lessons_lookup")]
    public class C4LLesson
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("language")]
        public string Language { get; set; }

        [Column("title")]
        public string Title { get; set; }

        [Column("unit")]
        public int Unit { get; set; }

        [Column("lesson_week")]
        public int Week { get; set; }

        [Column("lesson_day")]
        public int Day { get; set; }

        [Column("lesson_type")]
        public string LessonType { get; set; }

        [Column("lesson_type_sequence")]
        public int LessonTypeSequence { get; set; }

        [Column("title_sequence")]
        public int TitleSequence { get; set; }

        // These properties store the original curriculum information
        // They are initialized in the LessonService
        [NotMapped]
        public int OriginalUnit { get; set; }

        [NotMapped]
        public int OriginalWeek { get; set; }

        [NotMapped]
        public int OriginalDay { get; set; }

        [NotMapped]
        public int CalendarDay { get; set; }
    }
}
