﻿using Compass.C4L.Interfaces.Services;
using Compass.Common.Data;
using Compass.Common.Interfaces.Services;
using Compass.Common.Resources;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.Student
{
    public partial class StudentSummaryComponent : IDisposable
    {
        [Inject]
        public required NavigationManager NavigationManager { get; set; }
        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }
        [Inject]
        public required ILicensePoolService LicensePoolService { get; set; }

        private bool isLoading = true;
        private string fullName = string.Empty;
        private string schoolId = string.Empty;
        private string gender = string.Empty;
        private DateTime? birthDate;
        private DateTime? enrollDate;

        private long? studentId;
        private long? organizationId;

        private bool hasLAPLicense = false;
        private bool hasDECALicense = false;
        private bool hasC4LLicense = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        private async Task CheckLicenseProducts()
        {
            hasLAPLicense = await LicensePoolService.CheckActiveLicense(this.organizationId, CompassResource.LAP);

            hasDECALicense = await LicensePoolService.CheckActiveLicense(this.organizationId, CompassResource.DECA);

            hasC4LLicense = await C4LClassroomService.GetStudentC4LAccessAsync(this.organizationId, this.studentId);
        }

        protected override async Task OnInitializedAsync()
        {
            isLoading = true;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;
                studentId = commonSessionData.CurrentStudentId;

                if (studentId.HasValue)
                {
                    Compass.Common.Models.Student? student = await StudentService.GetStudentAsync(studentId);
                    if (student != null)
                    {
                        fullName = string.IsNullOrEmpty(student.MiddleName)
                            ? $"{student.FirstName} {student.LastName}"
                            : $"{student.FirstName} {student.MiddleName} {student.LastName}";
                        schoolId = student.SchoolId;
                        gender = student.Gender;
                        birthDate = student.BirthDate;
                        enrollDate = student.EnrollDate;

                        await CheckLicenseProducts();
                    }
                }

                isLoading = false;
            }
        }

        private void OnLAPOptionClick()
        {
            NavigationManager.NavigateTo($"/lap-student-summary");
        }

        private void OnDECAOptionClick()
        {
            NavigationManager.NavigateTo($"/deca-student-summary");
        }

        private void OnC4LOptionClick()
        {
            NavigationManager.NavigateTo($"/c4l-student-summary");
        }

        private void UpdateLocalizedValues()
        {
            string culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
