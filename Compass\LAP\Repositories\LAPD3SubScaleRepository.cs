using Compass.Common.Data;
using Compass.LAP.Interfaces.Repositories;
using Compass.LAP.Models;
using Microsoft.EntityFrameworkCore;

namespace Compass.LAP.Repositories
{
    public class LAPD3SubScaleRepository : ILAPD3SubScaleRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public LAPD3SubScaleRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<LAPD3SubScale> SaveAsync(LAPD3SubScale lapd3SubScale)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (lapd3SubScale.Id == 0)
                {
                    // Create new record
                    _dbContext.LAPD3SubScales.Add(lapd3SubScale);
                }
                else
                {
                    // Update existing record
                    _dbContext.LAPD3SubScales.Update(lapd3SubScale);
                }

                await _dbContext.SaveChangesAsync();
                return lapd3SubScale;
            }
        }

        public async Task<LAPD3SubScale?> GetByIdAsync(long? organizationId, long? id, long? assessmentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (assessmentId is null)
            {
                throw new ArgumentNullException(nameof(assessmentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.LAPD3SubScales
                    .FirstOrDefaultAsync(l => l.Id == id.Value && l.OrganizationId == organizationId.Value && l.AssessmentId == assessmentId.Value);
            }
        }
    }
}
