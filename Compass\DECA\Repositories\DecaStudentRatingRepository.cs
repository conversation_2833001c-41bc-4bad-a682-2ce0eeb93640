using Compass.Common.Data;
using Compass.DECA.DTOs;
using Compass.DECA.Interfaces.Repositories;
using Compass.Deca.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Runtime.CompilerServices;

namespace Compass.DECA.Repositories
{
    public class DecaStudentRatingRepository : IDecaStudentRatingRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public DecaStudentRatingRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        private void populateRatingFields(DecaStudentRating? rating, StudentGroupRatingDto dto)
        {
            // If the student has a rating, populate the rating fields
            if (rating != null)
            {
                dto.RatingId = rating.Id;
                dto.RatingDate = rating.RatingDate;
                dto.RatingYear = rating.RatingYear;
                dto.RatingPeriod = rating.RatingPeriod;
                dto.RecordForm = rating.RecordForm;
                dto.RatingLevel = rating.RatingLevel;
                dto.RaterType = rating.RaterType;
                dto.AgeAtRatingMonths = rating.AgeAtRatingMonths;

                // IN (Inattention) Scale
                dto.InRaw = rating.InRaw;
                dto.InTScore = rating.InTScore;
                dto.InPercentile = rating.InPercentile;
                dto.InDescription = rating.InDescription;

                // SC (Self-Control) Scale
                dto.ScRaw = rating.ScRaw;
                dto.ScTScore = rating.ScTScore;
                dto.ScPercentile = rating.ScPercentile;
                dto.ScDescription = rating.ScDescription;

                // AT (Attention) Scale
                dto.ArRaw = rating.ArRaw;
                dto.ArTScore = rating.ArTScore;
                dto.ArPercentile = rating.ArPercentile;
                dto.AtDescription = rating.AtDescription?.ToString();

                // TPF (Task Planning/Focus) Scale
                dto.TpfRaw = rating.TpfRaw;
                dto.TpfTScore = rating.TpfTScore;
                dto.TpfPercentile = rating.TpfPercentile;
                dto.TpfDescription = rating.TpfDescription?.ToString();

                // BC (Behavioral Control) Scale
                dto.BcRaw = rating.BcRaw;
                dto.BcTScore = rating.BcTScore;
                dto.BcPercentile = rating.BcPercentile;
                dto.BcDescription = rating.BcDescription?.ToString();

                // PR (Peer Relations) Scale
                dto.PrRaw = rating.PrRaw;
                dto.PrTScore = rating.PrTScore;
                dto.PrPercentile = rating.PrPercentile;
                dto.PrDescription = rating.PrDescription?.ToString();

                // OT (Organization/Time Management) Scale
                dto.OtRaw = rating.OtRaw;
                dto.OtTScore = rating.OtTScore;
                dto.OtPercentile = rating.OtPercentile;
                dto.OtDescription = rating.OtDescription?.ToString();

                // GB (Goal-directed Behavior) Scale
                dto.GbRaw = rating.GbRaw;
                dto.GbTScore = rating.GbTScore;
                dto.GbPercentile = rating.GbPercentile;
                dto.GbDescription = rating.GbDescription?.ToString();

                // SO (Social) Scale
                dto.SoRaw = rating.SoRaw;
                dto.SoTScore = rating.SoTScore;
                dto.SoPercentile = rating.SoPercentile;
                dto.SoDescription = rating.SoDescription?.ToString();

                // DM (Decision Making) Scale
                dto.DmRaw = rating.DmRaw;
                dto.DmTScore = rating.DmTScore;
                dto.DmPercentile = rating.DmPercentile;
                dto.DmDescription = rating.DmDescription?.ToString();

                // RS (Response to Stress) Scale
                dto.RsRaw = rating.RsRaw;
                dto.RsTScore = rating.RsTScore;
                dto.RsPercentile = rating.RsPercentile;
                dto.RsDescription = rating.RsDescription?.ToString();

                // SA (Social Awareness) Scale
                dto.SaRaw = rating.SaRaw;
                dto.SaTScore = rating.SaTScore;
                dto.SaPercentile = rating.SaPercentile;
                dto.SaDescription = rating.SaDescription?.ToString();

                // SM (Self-Management) Scale
                dto.SmRaw = rating.SmRaw;
                dto.SmTScore = rating.SmTScore;
                dto.SmPercentile = rating.SmPercentile;
                dto.SmDescription = rating.SmDescription?.ToString();

                // SEC (Social-Emotional Composite) Scale
                dto.SecRaw = rating.SecRaw;
                dto.SecTScore = rating.SecTScore;
                dto.SecPercentile = rating.SecPercentile;
                dto.SecDescription = rating.SecDescription?.ToString();
            }
        }

        public async Task<List<StudentGroupRatingDto>> GetCurrentStudentRatingsForStudentGroupAsync(long studentGroupId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    // Get the active school year for the site linked to the student group
                    var studentGroup = await _dbContext.StudentGroups.FindAsync(studentGroupId);
                    if (studentGroup == null)
                    {
                        return new List<StudentGroupRatingDto>();
                    }

                    var activeSchoolYear = await _dbContext.SchoolYears
                        .Where(sy => sy.SiteId == studentGroup.SiteId && sy.Status == "Current")
                        .FirstOrDefaultAsync();

                    if (activeSchoolYear == null)
                    {
                        return new List<StudentGroupRatingDto>();
                    }

                    // Get all students in the student group for the active school year
                    var studentsInGroup = await _dbContext.StudentGroupRosters
                        .Where(sgr => sgr.StudentGroupId == studentGroupId && sgr.SchoolYearId == activeSchoolYear.Id)
                        .Join(_dbContext.Students,
                            sgr => sgr.StudentId,
                            s => s.Id,
                            (sgr, s) => new
                            {
                                StudentId = s.Id,
                                s.FirstName,
                                s.LastName,
                                s.BirthDate
                            })
                        .ToListAsync();

                    // Get all ratings for these students for the active school year
                    var studentIds = studentsInGroup.Select(s => s.StudentId).ToList();
                    var ratings = await _dbContext.Set<DecaStudentRating>()
                        .Where(r => studentIds.Contains(r.StudentId) && r.RatingYear == activeSchoolYear.SchoolYearValue)
                        .ToListAsync();

                    // Create DTOs for each rating, including students without ratings
                    var result = new List<StudentGroupRatingDto>();
                    foreach (var student in studentsInGroup)
                    {
                        var studentRatings = ratings.Where(r => r.StudentId == student.StudentId).ToList();

                        if (studentRatings.Any())
                        {
                            // Create a DTO for each rating this student has
                            foreach (var rating in studentRatings)
                            {
                                var dto = new StudentGroupRatingDto
                                {
                                    StudentId = student.StudentId,
                                    FirstName = student.FirstName,
                                    LastName = student.LastName,
                                    BirthDate = student.BirthDate
                                };

                                // Populate the rating fields
                                populateRatingFields(rating, dto);

                                result.Add(dto);
                            }
                        }
                        else
                        {
                            // Student has no ratings, create an empty DTO
                            var dto = new StudentGroupRatingDto
                            {
                                StudentId = student.StudentId,
                                FirstName = student.FirstName,
                                LastName = student.LastName,
                                BirthDate = student.BirthDate
                            };

                            // No rating to populate, so rating fields will remain null
                            result.Add(dto);
                        }
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error getting student ratings for student group: {ex.Message}");
                throw;
            }
        }

        public async Task<List<StudentGroupRatingDto>> GetStudentRatingsForStudentAsync(long studentId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    // Get the student
                    var student = await _dbContext.Students.FindAsync(studentId);
                    if (student == null)
                    {
                        return new List<StudentGroupRatingDto>();
                    }

                    // Get all ratings for the student
                    var ratings = await _dbContext.Set<DecaStudentRating>()
                        .Where(r => studentId == studentId)
                        .ToListAsync();

                    // Create DTOs for each rating
                    var result = new List<StudentGroupRatingDto>();
                    foreach (var rating in ratings)
                    {
                        var dto = new StudentGroupRatingDto
                        {
                            StudentId = studentId,
                            FirstName = student.FirstName,
                            LastName = student.LastName,
                            BirthDate = student.BirthDate
                        };

                        // If the student has a rating, populate the rating fields
                        populateRatingFields(rating, dto);

                        result.Add(dto);
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error getting student ratings for student group: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentRating?> GetByIdAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaStudentRating>().FindAsync(id);
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error getting student rating by ID: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentRating> CreateAsync(DecaStudentRating rating)
        {
            try
            {
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                rating.ModId = userId;
                rating.ModTs = DateTime.Now;

                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    await _dbContext.Set<DecaStudentRating>().AddAsync(rating);
                    await _dbContext.SaveChangesAsync();
                    return rating;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error creating student rating: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentRating?> UpdateAsync(long id, DecaStudentRating rating)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    var existingRating = await _dbContext.Set<DecaStudentRating>().FindAsync(id);
                    if (existingRating == null)
                    {
                        return null;
                    }

                    var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                    var user = authState.User;
                    var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                    // Update the existing rating with the new values
                    existingRating.ModId = userId;
                    existingRating.ModTs = DateTime.Now;
                    existingRating.Status = rating.Status;
                    existingRating.RatingDate = rating.RatingDate;
                    existingRating.RatingYear = rating.RatingYear;
                    existingRating.RatingPeriod = rating.RatingPeriod;
                    existingRating.RecordForm = rating.RecordForm;
                    existingRating.RatingLevel = rating.RatingLevel;
                    existingRating.RaterId = rating.RaterId;
                    existingRating.RaterType = rating.RaterType;
                    existingRating.AgeAtRatingMonths = rating.AgeAtRatingMonths;

                    // Update all the scale values
                    // IN Scale
                    existingRating.InRaw = rating.InRaw;
                    existingRating.InTScore = rating.InTScore;
                    existingRating.InPercentile = rating.InPercentile;
                    existingRating.InDescription = rating.InDescription;

                    // SC Scale
                    existingRating.ScRaw = rating.ScRaw;
                    existingRating.ScTScore = rating.ScTScore;
                    existingRating.ScPercentile = rating.ScPercentile;
                    existingRating.ScDescription = rating.ScDescription;

                    // AT Scale
                    existingRating.ArRaw = rating.ArRaw;
                    existingRating.ArTScore = rating.ArTScore;
                    existingRating.ArPercentile = rating.ArPercentile;
                    existingRating.AtDescription = rating.AtDescription;

                    // TPF Scale
                    existingRating.TpfRaw = rating.TpfRaw;
                    existingRating.TpfTScore = rating.TpfTScore;
                    existingRating.TpfPercentile = rating.TpfPercentile;
                    existingRating.TpfDescription = rating.TpfDescription;

                    // BC Scale
                    existingRating.BcRaw = rating.BcRaw;
                    existingRating.BcTScore = rating.BcTScore;
                    existingRating.BcPercentile = rating.BcPercentile;
                    existingRating.BcDescription = rating.BcDescription;

                    // PR Scale
                    existingRating.PrRaw = rating.PrRaw;
                    existingRating.PrTScore = rating.PrTScore;
                    existingRating.PrPercentile = rating.PrPercentile;
                    existingRating.PrDescription = rating.PrDescription;

                    // OT Scale
                    existingRating.OtRaw = rating.OtRaw;
                    existingRating.OtTScore = rating.OtTScore;
                    existingRating.OtPercentile = rating.OtPercentile;
                    existingRating.OtDescription = rating.OtDescription;

                    // GB Scale
                    existingRating.GbRaw = rating.GbRaw;
                    existingRating.GbTScore = rating.GbTScore;
                    existingRating.GbPercentile = rating.GbPercentile;
                    existingRating.GbDescription = rating.GbDescription;

                    // SO Scale
                    existingRating.SoRaw = rating.SoRaw;
                    existingRating.SoTScore = rating.SoTScore;
                    existingRating.SoPercentile = rating.SoPercentile;
                    existingRating.SoDescription = rating.SoDescription;

                    // DM Scale
                    existingRating.DmRaw = rating.DmRaw;
                    existingRating.DmTScore = rating.DmTScore;
                    existingRating.DmPercentile = rating.DmPercentile;
                    existingRating.DmDescription = rating.DmDescription;

                    // RS Scale
                    existingRating.RsRaw = rating.RsRaw;
                    existingRating.RsTScore = rating.RsTScore;
                    existingRating.RsPercentile = rating.RsPercentile;
                    existingRating.RsDescription = rating.RsDescription;

                    // SA Scale
                    existingRating.SaRaw = rating.SaRaw;
                    existingRating.SaTScore = rating.SaTScore;
                    existingRating.SaPercentile = rating.SaPercentile;
                    existingRating.SaDescription = rating.SaDescription;

                    // SM Scale
                    existingRating.SmRaw = rating.SmRaw;
                    existingRating.SmTScore = rating.SmTScore;
                    existingRating.SmPercentile = rating.SmPercentile;
                    existingRating.SmDescription = rating.SmDescription;

                    // SEC Scale
                    existingRating.SecRaw = rating.SecRaw;
                    existingRating.SecTScore = rating.SecTScore;
                    existingRating.SecPercentile = rating.SecPercentile;
                    existingRating.SecDescription = rating.SecDescription;

                    _dbContext.Set<DecaStudentRating>().Update(existingRating);
                    await _dbContext.SaveChangesAsync();
                    return existingRating;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error updating student rating: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    var rating = await _dbContext.Set<DecaStudentRating>().FindAsync(id);
                    if (rating == null)
                    {
                        return false;
                    }

                    _dbContext.Set<DecaStudentRating>().Remove(rating);
                    await _dbContext.SaveChangesAsync();
                    return true;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error deleting student rating: {ex.Message}");
                throw;
            }
        }
    }
}
