using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Compass.Common.Repositories;
using Serilog;

namespace Compass.C4L.Services
{
    public class C4LLessonPreparationCompletedService : IC4LLessonPreparationCompletedService
    {
        private readonly IC4LLessonPreparationRepository _lessonPreparationRepository;
        private readonly IC4LLessonPreparationCompletedRepository _repository;
        private readonly IC4LClassroomRepository _classroomRepository;
        private readonly IStudentGroupRepository _studentGroupRepository;
        private readonly ISchoolYearRepository _schoolYearRepository;

        public C4LLessonPreparationCompletedService(
            IC4LLessonPreparationRepository lessonPreparationRepository,
            IC4LLessonPreparationCompletedRepository repository,
            IC4LClassroomRepository classroomRepository,
            IStudentGroupRepository studentGroupRepository,
            ISchoolYearRepository schoolYearRepository)
        {
            _lessonPreparationRepository = lessonPreparationRepository;
            _repository = repository;
            _classroomRepository = classroomRepository;
            _studentGroupRepository = studentGroupRepository;
            _schoolYearRepository = schoolYearRepository;
        }

        private async Task<(long?, int?)> GetCurrentSchoolYearAsync(int? schoolYear, long c4lClassroomId)
        {
            int? currentSchoolYear = schoolYear;
            long? organizationId = 0;
            if (currentSchoolYear is null || currentSchoolYear <= 0)
            {
                C4LClassroom? c4lClassroom = await _classroomRepository.GetC4LClassroomByIdAsync(c4lClassroomId);
                if (c4lClassroom == null)
                {
                    throw new Exception($"C4L Classroom does not exist for C4L Classroom Id {c4lClassroomId}");
                }

                organizationId = c4lClassroom.OrganizationId;
                SchoolYear? sy = await _schoolYearRepository.GetSchoolYearByIdAsync(c4lClassroom.SchoolYearId);
                if (sy == null)
                {
                    throw new Exception($"School Year does not exist for C4L Classroom Id {c4lClassroomId}");
                }

                currentSchoolYear = sy.SchoolYearValue;
            }

            return (organizationId, currentSchoolYear);
        }

        private async Task<(long ?, int?, C4LLessonPreparationDto?)> GetPreparationAsync(long preparationId, int? schoolYear, long c4lClassroomId)
        {
            if (preparationId <= 0)
            {
                throw new Exception($"preparationId must be set {preparationId}");
            }

            (long? organizationId, int? schoolYear) results = await GetCurrentSchoolYearAsync(schoolYear, c4lClassroomId);

            int? currentSchoolYear = results.schoolYear;
            long? organizationId = results.organizationId;

            C4LLessonPreparationDto? prop = await _lessonPreparationRepository.GetPreparationByIdAsync(preparationId);
            if (prop == null)
            {
                throw new Exception($"Preparation does not exist for Preparation Id {preparationId}");
            }

            return (organizationId, currentSchoolYear, prop);
        }

        public async Task<C4LLessonPreparationCompleted?> GetAsync(C4LLessonPreparationCompleted? preparationCompleted)
        {
            C4LLessonPreparationCompleted? ret = null;
            try
            {
                if (preparationCompleted == null)
                {
                    throw new ArgumentNullException(nameof(preparationCompleted));
                }

                if (preparationCompleted.OrganizationId <= 0)
                {
                    throw new ArgumentNullException(nameof(preparationCompleted.OrganizationId));
                }

                if (preparationCompleted.C4LClassroomId <= 0)
                {
                    throw new ArgumentNullException(nameof(preparationCompleted.C4LClassroomId));
                }

                if ( preparationCompleted.Schoolyear <= 0)
                {
                    (long? organizationId, int? schoolYear) results = await GetCurrentSchoolYearAsync(preparationCompleted.Schoolyear, preparationCompleted.C4LClassroomId);

                    if ( results.schoolYear == null || results.schoolYear <= 0)
                    {
                        throw new Exception($"School Year does not exist for C4L Classroom Id {preparationCompleted.C4LClassroomId}");
                    }

                    C4LLessonPreparationCompleted? prepCompleted = new C4LLessonPreparationCompleted()
                    {
                        OrganizationId = preparationCompleted.OrganizationId,
                        C4LClassroomId = preparationCompleted.C4LClassroomId,
                        Unit = preparationCompleted.Unit,
                        Week = preparationCompleted.Week,
                        Day = preparationCompleted.Day,
                        LessonTypeSequence = preparationCompleted.LessonTypeSequence,
                        TitleSequence = preparationCompleted.TitleSequence,
                        Schoolyear = results.schoolYear.Value
                    };

                    ret = await _repository.GetByAsync(prepCompleted);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.GetByPreparationIdAndSchoolYearAsync for PreparationId");
                throw;
            }

            return ret;
        }

        public async Task<C4LLessonPreparationCompleted?> GetAsync(long preparationId, int? schoolYear, long c4lClassroomId)
        {
            try
            {
                (long? organizationId, int? currentSchoolYear, C4LLessonPreparationDto? prop) propResult = await GetPreparationAsync(preparationId, schoolYear, c4lClassroomId);
                if (propResult.prop == null)
                {
                    throw new Exception($"Preparation does not exist for Preparation Id {preparationId}");
                }

                if (propResult.organizationId == null || propResult.organizationId <= 0)
                {
                   throw new Exception($"Organization does not exist for C4L Classroom Id {c4lClassroomId}");
                }

                if ( propResult.currentSchoolYear  == null || propResult.currentSchoolYear <= 0)
                {
                    throw new Exception($"School Year does not exist for C4L Classroom Id {c4lClassroomId}");
                }

                C4LLessonPreparationCompleted? preparationCompleted = new C4LLessonPreparationCompleted()
                {
                    OrganizationId = propResult.organizationId.Value,
                    C4LClassroomId = c4lClassroomId,
                    Unit = propResult.prop.Unit,
                    Week = propResult.prop.Week,
                    Day = propResult.prop.Day,
                    LessonTypeSequence = propResult.prop.LessonTypeSequence,
                    TitleSequence = propResult.prop.TitleSequence,
                    Schoolyear = propResult.currentSchoolYear.Value
                };

                return await _repository.GetByAsync(preparationCompleted);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.GetByPreparationIdAndSchoolYearAsync for PreparationId");
                throw;
            }
        }

        private async Task<C4LLessonPreparationCompleted> UpsertAsync(C4LLessonPreparationCompleted? preparationCompleted, bool isCompleted)
        {
            // Check if a record already exists
            C4LLessonPreparationCompleted? existingRecord = await _repository.GetByAsync(preparationCompleted);

            if (existingRecord == null)
            {
                // Create a new record
                C4LLessonPreparationCompleted newRecord = new C4LLessonPreparationCompleted
                {
                    OrganizationId = preparationCompleted.OrganizationId,
                    Schoolyear = preparationCompleted.Schoolyear,
                    C4LClassroomId = preparationCompleted.C4LClassroomId,
                    Unit = preparationCompleted.Unit,
                    Week = preparationCompleted.Week,
                    Day = preparationCompleted.Day,
                    LessonTypeSequence = preparationCompleted.LessonTypeSequence,
                    TitleSequence = preparationCompleted.TitleSequence,
                    DateCompleted = isCompleted ? DateTime.Now : null
                    // ModId and ModTs will be set in the repository
                };

                return await _repository.CreateAsync(newRecord);
            }
            else
            {
                // Update the existing record
                existingRecord.DateCompleted = isCompleted ? DateTime.Now : null;
                return await _repository.UpdateAsync(existingRecord);
            }

        }

        public async Task<C4LLessonPreparationCompleted> SaveCompletionStatusAsync(long preparationId, int? schoolYear, long c4lClassroomId, bool isCompleted)
        {
            try
            {
                (long? organizationId, int? currentSchoolYear, C4LLessonPreparationDto? prop) propResult = await GetPreparationAsync(preparationId, schoolYear, c4lClassroomId);

                if (propResult.prop == null)
                {
                    throw new Exception($"Preparation does not exist for Preparation Id {preparationId}");
                }

                if (propResult.organizationId == null || propResult.organizationId <= 0)
                {
                    throw new Exception($"Organization does not exist for C4L Classroom Id {c4lClassroomId}");
                }

                if (propResult.currentSchoolYear == null || propResult.currentSchoolYear <= 0)
                {
                    throw new Exception($"School Year does not exist for C4L Classroom Id {c4lClassroomId}");
                }

                C4LLessonPreparationCompleted? preparationCompleted = new C4LLessonPreparationCompleted()
                {
                    OrganizationId = propResult.organizationId.Value,
                    C4LClassroomId = c4lClassroomId,
                    Unit = propResult.prop.Unit,
                    Week = propResult.prop.Week,
                    Day = propResult.prop.Day,
                    LessonTypeSequence = propResult.prop.LessonTypeSequence,
                    TitleSequence = propResult.prop.TitleSequence,
                    Schoolyear = propResult.currentSchoolYear.Value
                };

                return await UpsertAsync(preparationCompleted, isCompleted);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.SaveCompletionStatusAsync");
                throw;
            }
        }

        public async Task<C4LLessonPreparationCompleted> SaveCompletionStatusAsync(C4LLessonPreparationCompleted? preparationCompleted, bool isCompleted)
        {
            try
            {
                if (preparationCompleted == null)
                {
                    throw new ArgumentNullException(nameof(preparationCompleted));
                }

                if (preparationCompleted.OrganizationId <= 0)
                {
                    throw new ArgumentNullException(nameof(preparationCompleted.OrganizationId));
                }

                if (preparationCompleted.C4LClassroomId <= 0)
                {
                    throw new ArgumentNullException(nameof(preparationCompleted.C4LClassroomId));
                }

                int? currentSchoolYear = preparationCompleted.Schoolyear;
                if (preparationCompleted.Schoolyear <= 0)
                {
                    (long? organizationId, int? schoolYear) results = await GetCurrentSchoolYearAsync(preparationCompleted.Schoolyear, preparationCompleted.C4LClassroomId);

                    if (results.schoolYear == null || results.schoolYear <= 0)
                    {
                        throw new Exception($"School Year does not exist for C4L Classroom Id {preparationCompleted.C4LClassroomId}");
                    }

                    preparationCompleted.Schoolyear = results.schoolYear.Value;
                }

                return await UpsertAsync(preparationCompleted, isCompleted);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.GetByPreparationIdAndSchoolYearAsync for PreparationId");
                throw;
            }
        }
    }
}
