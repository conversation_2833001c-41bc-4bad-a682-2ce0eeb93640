﻿using Compass.Common.Data;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class SchoolYearRepository : ISchoolYearRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SchoolYearRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<SchoolYear> CreateSchoolYearAsync(SchoolYear schoolYear)
        {
            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            schoolYear.ModId = userId;
            schoolYear.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.SchoolYears.AddAsync(schoolYear);
                await _dbContext.SaveChangesAsync();
            }

            return schoolYear;
        }

        public async Task<SchoolYear?> UpdateSchoolYearAsync(long? id, SchoolYear schoolYear)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (schoolYear is null)
            {
                return null;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                SchoolYear? existingSchoolYear = await _dbContext.SchoolYears.FindAsync(id);

                if (existingSchoolYear == null)
                {
                    return null;
                }

                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                existingSchoolYear.ModId = userId;
                existingSchoolYear.ModTs = DateTime.Now;

                existingSchoolYear.SchoolYearValue = schoolYear.SchoolYearValue;
                existingSchoolYear.Description = schoolYear.Description;
                existingSchoolYear.Status = schoolYear.Status;

                _dbContext.SchoolYears.Update(existingSchoolYear);
                await _dbContext.SaveChangesAsync();

                return existingSchoolYear;
            }
        }

        public async Task<SchoolYear?> GetExistingSchoolYearAsync(int year, long siteId, long organizationId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.SchoolYears
                        .FirstOrDefaultAsync(o => o.SchoolYearValue == year && o.SiteId == siteId && o.OrganizationId == organizationId);
            }
        }

        public async Task<int?> GetCurrentSchoolYearForSiteAsync(long? siteId, long? organizationId)
        {
            if (!siteId.HasValue || !organizationId.HasValue)
            {
                return null;
            }

            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                var currentSchoolYear = await dbContext.SchoolYears
                        .FirstOrDefaultAsync(o => o.SiteId == siteId && o.OrganizationId == organizationId && o.Status == "Current");

                return currentSchoolYear?.SchoolYearValue;
            }
        }

        public async Task<List<SchoolYear>> GetSchoolYearList(SchoolYearListAction action)
        {
            PageQuery pageQuery = action.PageQuery;
            string searchText = pageQuery.QueryText;
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            if (organizationId is null)
            {
                organizationId = -1;
            }

            long? siteId = action.SiteId;
            if (siteId is null)
            {
                siteId = -1;
            }

            bool isNumeric = int.TryParse(searchText, out int yearSearchValue);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<SchoolYear> resultList = await _dbContext.SchoolYears
                                            .Where(sy => sy.SiteId == siteId
                                                    && sy.OrganizationId == organizationId
                                                    &&
                                                    (
                                                        sy.Description != null && sy.Description.Contains(searchText) ||
                                                         isNumeric && sy.SchoolYearValue == yearSearchValue
                                                    ))
                                            .OrderBy(sy => sy.SchoolYearValue)
                                            .Skip(pageOffset)
                                            .Take(pageSize)
                                            .ToListAsync();
                return resultList;
            }
        }

        public async Task<int> GetSchoolYearCount(long? organizationId, long? siteId)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (siteId is null)
            {
                siteId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.SchoolYears.Where(o => o.SiteId == siteId && o.OrganizationId == organizationId).CountAsync();
                return count;
            }
        }

        public async Task<SchoolYear?> GetCurrentSchoolYear(long? organizationId, long? siteId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.SchoolYears
                        .FirstOrDefaultAsync(sy => sy.SiteId == siteId
                                                    && sy.OrganizationId == organizationId
                                                    && sy.Status == "Current");
            }
        }

        public async Task<int> CloseSchoolYears(long? organizationId, long? siteId)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (siteId is null)
            {
                siteId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Find all matching records with explicit types
                List<SchoolYear> schoolYearsToUpdate = await _dbContext.SchoolYears
                .Where(sy => sy.OrganizationId == organizationId && sy.SiteId == siteId)
                .ToListAsync();

                // Update each entity
                foreach (SchoolYear schoolYear in schoolYearsToUpdate)
                {
                    schoolYear.Status = "Closed";
                    _dbContext.SchoolYears.Update(schoolYear);
                }

                // Save changes to the database
                int recordsUpdated = await _dbContext.SaveChangesAsync();

                return recordsUpdated;
            }
        }

        public async Task<SchoolYear?> GetSchoolYearByIdAsync(long id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.SchoolYears.FirstOrDefaultAsync(o => o.Id == id);
            }
        }
    }
}
